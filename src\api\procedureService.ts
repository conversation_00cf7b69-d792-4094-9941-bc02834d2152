import { AUTH_CONFIG } from '../config/authConfig';
import { getApiUrl } from '../config/apiConfig';


async function fetchWithRetry(url: string, options: RequestInit, retries = 2, delay = 1000): Promise<Response> {
  try {
    const response = await fetch(url, options);
    return response;
  } catch (err) {
    console.error(`Fetch error for ${url}:`, err);
    if (err instanceof TypeError && err.message.includes('cors')) {
      console.error('CORS ERROR: This appears to be a Cross-Origin Request issue.');
      console.error('Check that the Vite dev server is running and proxy is configured correctly.');
    }
    if (retries <= 0) throw err;
    await new Promise(resolve => setTimeout(resolve, delay));
    return fetchWithRetry(url, options, retries - 1, delay);
  }
}

export async function callProcedure<T>(
  procedure: string, 
  parameters: Record<string, string | number | boolean | null | undefined>,
  dbType: 'sql' | 'pg' = 'sql'  
): Promise<T> {
  try {
    const url = getApiUrl(dbType);
    const token = localStorage.getItem(AUTH_CONFIG.tokenKey);   
    if (!token) {
      console.warn('Authentication token is missing');
    } 
    const requestBody = {
      procedure,
      parametros: parameters
    };

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      },
      body: JSON.stringify(requestBody)
    };
    
    const response = await fetchWithRetry(url, options);

    if (!response.ok) {
      const errorText = await response.text().catch(() => '');
      console.error(`Server responded with ${response.status}: ${errorText || 'No error details'}`);
      if (response.status === 401 || response.status === 403) {
        console.error('Authentication failed. Token may be invalid or expired.');
      } else if (response.status === 500) {
        console.error('Server error. Backend might be unavailable or experiencing issues.');
      }     
      throw new Error(`HTTP error! status: ${response.status}${errorText ? `, message: ${errorText}` : ''}`);
    }

    const data = await response.json();
    return data as T;
  } catch (error) {
    console.error(`Error calling procedure ${procedure}:`, error);
    throw error;
  }
} 