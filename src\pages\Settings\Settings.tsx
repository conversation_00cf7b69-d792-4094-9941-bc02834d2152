import React from 'react';
import { Container, Row, Col, Card, Form } from 'react-bootstrap';
import { FA6 } from '@/config';
import { PageMetadata } from '@/hoc/withPageMetadata';
import { PERMISSIONS } from '@/config/authConfig';
import { useTheme } from '@/context/ThemeContext';
import { useLayout } from '@/context/LayoutContext';
import { useAppConfig } from '@/config/appConfig';
import { ColorTheme, themeColors } from '@/config/themeConfig';

const Settings: React.FC & { pageMetadata: PageMetadata } = () => {
  const { getValue, setValue } = useAppConfig();

  const {
    settings: themeSettings,
    toggleTheme,
    toggleCardBorder,
    setColorTheme
  } = useTheme();

  const {
    settings: layoutSettings,
    toggleBoxedLayout,
    setLayout,
    setDirection
  } = useLayout();

  const colorThemes = Object.entries(themeColors).map(([name, colors]) => ({
    name: name as ColorTheme,
    color: colors.primary,
    label: name.replace('_Theme', '')
  }));

  return (
    <Container fluid>
      <Row>
        <Col lg={6}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title>Configuración del Tema</Card.Title>
            </Card.Header>
            <Card.Body>
              <Form>
                {/* Modo Claro/Oscuro */}
                <Form.Group className="mb-4">
                  <Form.Label>Modo de tema</Form.Label>
                  <div className="d-flex">
                    <Form.Check
                      type="radio"
                      id="theme-light"
                      label="Claro"
                      name="theme-mode"
                      className="me-3"
                      checked={themeSettings.theme === 'light'}
                      onChange={() => themeSettings.theme !== 'light' && toggleTheme()}
                    />
                    <Form.Check
                      type="radio"
                      id="theme-dark"
                      label="Oscuro"
                      name="theme-mode"
                      checked={themeSettings.theme === 'dark'}
                      onChange={() => themeSettings.theme !== 'dark' && toggleTheme()}
                    />
                  </div>
                </Form.Group>

                {/* Layout Sidebar */}
                <Form.Group className="mb-4">
                  <Form.Label>Tipo de Sidebar</Form.Label>
                  <div className="d-flex">
                    <Form.Check
                      type="radio"
                      id="layout-vertical"
                      label="Vertical"
                      name="layout-type"
                      className="me-3"
                      checked={layoutSettings.layout === 'vertical'}
                      onChange={() => layoutSettings.layout !== 'vertical' && setLayout('vertical')}
                    />
                    <Form.Check
                      type="radio"
                      id="layout-horizontal"
                      label="Horizontal"
                      name="layout-type"
                      checked={layoutSettings.layout === 'horizontal'}
                      onChange={() => layoutSettings.layout !== 'horizontal' && setLayout('horizontal')}
                    />
                  </div>
                </Form.Group>

                {/* Layout Width */}
                <Form.Group className="mb-4">
                  <Form.Label>Ancho del Layout</Form.Label>
                  <div className="d-flex">
                    <Form.Check
                      type="radio"
                      id="layout-boxed"
                      label="Boxed"
                      name="layout-width"
                      className="me-3"
                      checked={layoutSettings.boxedLayout}
                      onChange={() => !layoutSettings.boxedLayout && toggleBoxedLayout()}
                    />
                    <Form.Check
                      type="radio"
                      id="layout-full"
                      label="Full Width"
                      name="layout-width"
                      checked={!layoutSettings.boxedLayout}
                      onChange={() => layoutSettings.boxedLayout && toggleBoxedLayout()}
                    />
                  </div>
                </Form.Group>

                {/* Direction RTL/LTR */}
                <Form.Group className="mb-4">
                  <Form.Label>Dirección del texto</Form.Label>
                  <div className="d-flex">
                    <Form.Check
                      type="radio"
                      id="direction-ltr"
                      label="LTR (Izquierda a Derecha)"
                      name="direction"
                      className="me-3"
                      checked={layoutSettings.direction === 'ltr'}
                      onChange={() => layoutSettings.direction !== 'ltr' && setDirection('ltr')}
                    />
                    <Form.Check
                      type="radio"
                      id="direction-rtl"
                      label="RTL (Derecha a Izquierda)"
                      name="direction"
                      checked={layoutSettings.direction === 'rtl'}
                      onChange={() => layoutSettings.direction !== 'rtl' && setDirection('rtl')}
                    />
                  </div>
                </Form.Group>

                {/* Estilo de Tarjetas */}
                <Form.Group className="mb-4">
                  <Form.Label>Estilo de tarjetas</Form.Label>
                  <div className="d-flex">
                    <Form.Check
                      type="radio"
                      id="card-border"
                      label="Shadow"
                      name="card-style"
                      className="me-3"
                      checked={themeSettings.cardBorder}
                      onChange={() => !themeSettings.cardBorder && toggleCardBorder()}
                    />
                    <Form.Check
                      type="radio"
                      id="card-shadow"
                      label="Border"
                      name="card-style"
                      checked={!themeSettings.cardBorder}
                      onChange={() => themeSettings.cardBorder && toggleCardBorder()}
                    />
                  </div>
                </Form.Group>

                {/* Temas de color */}
                <Form.Group className="mb-4">
                  <Form.Label>Tema de color</Form.Label>
                  <Row className="g-2 mt-1">
                    {colorThemes.map((theme) => {
                      const isActive = themeSettings.colorTheme === theme.name;

                      return (
                        <Col key={theme.name} xs={4} className="mb-2">
                          <div
                            className={`theme-color ${isActive ? 'active' : ''}`}
                            onClick={() => setColorTheme(theme.name)}
                            style={{ cursor: 'pointer' }}
                          >
                            <div
                              className="theme-color-preview"
                              style={{
                                width: '30px',
                                height: '30px',
                                borderRadius: '50%',
                                backgroundColor: theme.color,
                                border: isActive ? '2px solid #000' : '1px solid #ddd'
                              }}
                            />
                            <span className="ms-2 d-block mt-1 small">
                              {theme.label}
                            </span>
                          </div>
                        </Col>
                      );
                    })}
                  </Row>
                </Form.Group>
              </Form>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={6}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title>Configuración de la Aplicación</Card.Title>
            </Card.Header>
            <Card.Body>
              <h5 className="mb-3">Preferencias de Usuario</h5>

              <div className="mb-3">
                <label className="form-label">Nombre</label>
                <input
                  type="text"
                  className="form-control"
                  value={getValue('userName', '')}
                  onChange={(e) => setValue('userName', e.target.value)}
                  placeholder="Tu nombre"
                />
              </div>

              <div className="mb-3">
                <label className="form-label">Email</label>
                <input
                  type="email"
                  className="form-control"
                  value={getValue('userEmail', '')}
                  onChange={(e) => setValue('userEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    id="receiveNotifications"
                    checked={getValue('receiveNotifications', true)}
                    onChange={(e) => setValue('receiveNotifications', e.target.checked)}
                  />
                  <label className="form-check-label" htmlFor="receiveNotifications">
                    Recibir notificaciones
                  </label>
                </div>
              </div>

              <div className="mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    id="saveHistory"
                    checked={getValue('saveHistory', true)}
                    onChange={(e) => setValue('saveHistory', e.target.checked)}
                  />
                  <label className="form-check-label" htmlFor="saveHistory">
                    Guardar historial de navegación
                  </label>
                </div>
              </div>

              <button className="btn btn-primary">
                Guardar Cambios
              </button>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
}

// Define page metadata directly in the component
Settings.pageMetadata = {
  title: 'Personalización',
  description: 'Personalización de la app',
  icon: <FA6.FaGear className="fs-6" />,
  showInMenu: false,
  menuOrder: 5,
  path: '/main/settings', // Use the MAIN_ROUTE_PREFIX
  requiresAuth: true,
  section: 'CONFIGURACIÓN', // Explicitly set the section
  permissions: {
    requiredPermission: PERMISSIONS.VIEW,
    resourceId: 'settings'
  }
};

export default Settings;
