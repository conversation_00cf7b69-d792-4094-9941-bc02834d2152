/**
 * FormRadio Component
 *
 * Un componente de radio button con soporte para diferentes variantes y estilos.
 *
 * @example
 * <FormRadio
 *   name="gender"
 *   label="Género"
 *   options={[
 *     { value: 'male', label: 'Masculino' },
 *     { value: 'female', label: 'Femenino' },
 *     { value: 'other', label: '<PERSON><PERSON>' }
 *   ]}
 *   value={gender}
 *   onChange={handleGenderChange}
 * />
 */

import React, { forwardRef } from 'react';
import { Form } from 'react-bootstrap';

export type RadioOption = {
  value: string | number;
  label: React.ReactNode;
  disabled?: boolean;
};

export interface FormRadioProps {
  /** ID del grupo (si no se proporciona, se usa el nombre) */
  id?: string;
  /** Nombre del grupo (requerido) */
  name: string;
  /** Etiqueta del grupo */
  label?: string;
  /** Texto de ayuda debajo del grupo */
  helpText?: string;
  /** Mensaje de error */
  error?: string;
  /** Si el campo es requerido */
  required?: boolean;
  /** Opciones de radio */
  options: RadioOption[];
  /** Valor seleccionado */
  value?: string | number;
  /** Si los radio buttons están en línea */
  inline?: boolean;
  /** Función para manejar el cambio de valor */
  onChange?: (value: string | number) => void;
}

const FormRadio = forwardRef<HTMLDivElement, FormRadioProps>(
  (
    {
      id,
      name,
      label,
      helpText,
      error,
      required = false,
      options = [],
      value,
      inline = false,
      onChange,
    },
    ref
  ) => {
    // Generar ID si no se proporciona
    const groupId = id || `form-radio-group-${name}`;

    // Manejar cambio de valor
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e.target.value);
      }
    };

    return (
      <div ref={ref} className="mb-3" id={groupId}>
        {label && (
          <label className={`form-label${required ? ' required' : ''}`}>
            {label}
          </label>
        )}

        <div>
          {options.map((option) => (
            <Form.Check
              key={`${name}-${option.value}`}
              type="radio"
              id={`${name}-${option.value}`}
              name={name}
              value={option.value.toString()}
              label={option.label}
              checked={value !== undefined && value.toString() === option.value.toString()}
              disabled={option.disabled}
              onChange={handleChange}
              inline={inline}
              isInvalid={!!error}
            />
          ))}
        </div>

        {error && (
          <div className="invalid-feedback d-block">
            {error}
          </div>
        )}

        {helpText && (
          <div className="form-text text-muted">
            {helpText}
          </div>
        )}
      </div>
    );
  }
);

FormRadio.displayName = 'FormRadio';

export default FormRadio;
