import React from 'react';
import { Card } from 'react-bootstrap';
import classNames from 'classnames';
import { FA6 } from '../../../config/icons/iconUtils';

export type EmptyStateIcon = 'search' | 'data' | 'error' | 'filter' | 'custom';

export interface EmptyStateProps {
  
  icon?: EmptyStateIcon;
  customIcon?: React.ReactNode;  
  title: string;  
  description?: string;  
  action?: React.ReactNode;  
  className?: string;  
  asCard?: boolean;  
  size?: 'sm' | 'md' | 'lg';
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'data',
  customIcon,
  title,
  description,
  action,
  className,
  asCard = false,
  size = 'md',
}) => {
  const getIcon = () => {
    if (icon === 'custom' && customIcon) {
      return customIcon;
    }
    
    switch (icon) {
      case 'search':
        return <FA6.FaSearchengin />;
      case 'error':
        return <FA6.FaTriangleExclamation />;
      case 'filter':
        return <FA6.FaFilter />;
      case 'data':
      default:
        return <FA6.FaDatabase />;
    }
  };
  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'fs-3';
      case 'lg':
        return 'fs-1';
      case 'md':
      default:
        return 'fs-2';
    }
  };

  const getTitleSize = () => {
    switch (size) {
      case 'sm':
        return 'fs-5';
      case 'lg':
        return 'fs-3';
      case 'md':
      default:
        return 'fs-4';
    }
  };

  const getPadding = () => {
    switch (size) {
      case 'sm':
        return 'p-3';
      case 'lg':
        return 'p-5';
      case 'md':
      default:
        return 'p-4';
    }
  };
  
  const content = (
    <div className={classNames('text-center', getPadding())}>
      <div className={classNames('mb-3 text-muted', getIconSize())}>
        {getIcon()}
      </div>
      <h5 className={classNames('mb-2', getTitleSize())}>{title}</h5>
      {description && (
        <p className="text-muted mb-3">{description}</p>
      )}
      {action && (
        <div className="mt-3">
          {action}
        </div>
      )}
    </div>
  );
  
  if (asCard) {
    return (
      <Card className={className}>
        {content}
      </Card>
    );
  }
  
  return (
    <div className={className}>
      {content}
    </div>
  );
};

export default EmptyState;
