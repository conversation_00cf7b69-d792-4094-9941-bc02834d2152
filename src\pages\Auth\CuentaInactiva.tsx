import React from 'react';
import { FA6 } from '@/config';

interface CuentaInactivaProps {
  handleVerifyStatus: () => void;
  isVerifying: boolean;
  statusLoading: boolean;
}

const CuentaInactiva: React.FC<CuentaInactivaProps> = ({
  handleVerifyStatus,
  isVerifying,
  statusLoading
}) => {
  return (
    <div className="d-flex justify-content-center align-items-center" style={{ minHeight: 'calc(100vh - 200px)' }}>
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-12 col-md-8 col-lg-6 col-xl-5">
            <div className="card border-0 shadow-lg" style={{ borderRadius: '20px' }}>
              <div className="card-body text-center p-5">
                <div className="mb-4">
                  <div
                    className="d-inline-flex align-items-center justify-content-center rounded-circle mb-3"
                    style={{
                      width: '80px',
                      height: '80px',
                      background: 'linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%)',
                      boxShadow: '0 8px 32px rgba(251, 191, 36, 0.3)'
                    }}
                  >
                    <FA6.FaUserSlash className="text-white" size={32} />
                  </div>
                </div>

                <h2 className="fw-bold mb-3" style={{ fontSize: '1.75rem' }}>
                  Cuenta Inactiva
                </h2>

                <p className="text-muted mb-4" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                  Tu cuenta de cliente se encuentra inactiva.
                  No es posible acceder a los datos en este momento.
                </p>

                <div className="card border-0 shadow-sm p-4 mb-4" style={{ background: 'rgba(251, 191, 36, 0.05)', borderRadius: '16px' }}>
                  <div className="d-flex align-items-center justify-content-center mb-2">
                    <FA6.FaHeadset className="text-warning me-2" size={20} />
                    <span className="fw-semibold">¿Necesitas ayuda?</span>
                  </div>
                  <p className="text-muted mb-0 small">
                    Contacta con R.R.H.H. para más información.
                  </p>
                </div>

                <button
                  className="btn btn-outline-primary px-4 py-2"
                  onClick={handleVerifyStatus}
                  disabled={isVerifying || statusLoading}
                  style={{ borderRadius: '12px' }}
                >
                  {(isVerifying || statusLoading) ? (
                    <>
                      <div className="spinner-border spinner-border-sm me-2" role="status">
                        <span className="visually-hidden">Verificando...</span>
                      </div>
                      Verificando...
                    </>
                  ) : (
                    <>
                      <FA6.FaArrowRotateRight className="me-2" size={14} />
                      Verificar nuevamente
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CuentaInactiva;