import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import SimpleBar from 'simplebar-react';
import 'simplebar-react/dist/simplebar.min.css';
import { buildNavigationFromRoutes, routes, NavigationItem } from '@/routes/PageMetadata';
import { FA6 } from '@/config/icons/iconUtils';
import { menuStructure } from '@/hoc/withPageMetadata';

// Define types for quick links
interface QuickLink {
  title: string;
  path: string;
  description?: string;
  icon?: React.ReactNode;
  section?: string;
}

interface DdSearchbarProps {
  isOpen: boolean;
  onClose: () => void;
}

const DdSearchbar: React.FC<DdSearchbarProps> = ({ isOpen, onClose }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [quickLinks, setQuickLinks] = useState<QuickLink[]>([]);

  // Group links by section using centralized configuration
  const groupBySection = (links: QuickLink[]) => {
    // Initialize groups based on menuStructure
    const groups: Record<string, QuickLink[]> = {};

    // Create an empty array for each section in menuStructure
    Object.keys(menuStructure).forEach(section => {
      groups[section] = [];
    });

    // Add a default Home section if it doesn't exist
    if (!groups['HOME']) {
      groups['HOME'] = [];
    }

    links.forEach(link => {
      // Extract the key from the path (remove '/main/' prefix)
      const pathParts = link.path.split('/');
      const key = pathParts[pathParts.length - 1];

      // Find which section this link belongs to
      let foundSection = false;

      for (const [section, pages] of Object.entries(menuStructure)) {
        // Check if any page in this section matches the key
        if (pages.some(page => page.toLowerCase() === key ||
                              page.toLowerCase().includes(key))) {
          groups[section].push(link);
          foundSection = true;
          break;
        }
      }

      // If not found in any section, put in HOME
      if (!foundSection) {
        groups['HOME'].push(link);
      }
    });

    return groups;
  };

  // Extract links from navigation items
  const extractLinksFromNavigation = (items: NavigationItem[]): QuickLink[] => {
    const links: QuickLink[] = [];

    const processItem = (item: NavigationItem) => {
      links.push({
        title: item.title,
        path: item.path,
        icon: item.icon,
        section: item.section // Include the section property
      });

      if (item.children) {
        item.children.forEach(processItem);
      }
    };

    items.forEach(processItem);
    return links;
  };

  // Use buildNavigationFromRoutes to get the same navigation structure as sidebar
  useEffect(() => {
    const navigationItems = buildNavigationFromRoutes(routes);
    const links = extractLinksFromNavigation(navigationItems);
    setQuickLinks(links);
  }, []);

  // Filter links based on search term
  const filteredLinks = searchTerm
    ? quickLinks.filter(
        link =>
          link.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          link.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (link.description && link.description.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : quickLinks;

  // Group filtered links
  const groupedLinks = groupBySection(filteredLinks);

  // If modal is closed, don't render to optimize
  if (!isOpen) return null;

  // Proporcionar un icono predeterminado para enlaces sin icono
  const defaultIcon = <FA6.FaLink className="fs-5 text-primary" />;

  return (
    <>
      {/* Modal backdrop overlay */}
      <div
        className="modal-backdrop fade show"
        style={{ opacity: '0.5' }}
        onClick={onClose}
      ></div>

      {/* Modal dialog */}
      <div
        className="modal fade show"
        id="exampleModal"
        tabIndex={-1}
        aria-hidden="false"
        style={{ display: 'block' }}
      >
        <div className="modal-dialog modal-dialog-scrollable modal-lg">
          <div className="modal-content rounded-1">
            <div className="modal-header border-bottom">
              <input
                type="search"
                className="form-control"
                placeholder="Buscar"
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                autoFocus
                onKeyDown={(e) => {
                  // Close on Escape
                  if (e.key === 'Escape') onClose();
                }}
              />
              <button
                type="button"
                className="btn-close"
                onClick={onClose}
                aria-label="Close"
              ></button>
            </div>
            <div className="modal-body p-3">
              <SimpleBar style={{ maxHeight: '60vh' }}>
                {Object.keys(groupedLinks).map(section =>
                  groupedLinks[section].length > 0 && (
                    <div key={section} className="mb-4">
                      <div className="d-flex align-items-center mb-2">
                        <div className="bg-primary" style={{width: "3px", height: "14px", marginRight: "8px", opacity: 0.4}}></div>
                        <h6 className="text-primary mb-0">{section}</h6>
                      </div>

                      <div className="ps-3 border-start" style={{ borderColor: '#eaeaea !important' }}>
                        {groupedLinks[section].map((link, index) => (
                          <Link
                            key={index}
                            to={link.path}
                            onClick={onClose}
                            className="d-block text-decoration-none py-1 border-bottom"
                            style={{ borderColor: '#f5f5f5 !important' }}
                          >
                            <div className="d-flex align-items-center position-relative" style={{ minHeight: link.description ? '42px' : '24px' }}>
                              {/* Usar el icono directamente del enlace o mostrar uno predeterminado si no existe */}
                              <div className="me-2" style={{ width: '28px', textAlign: 'center' }}>
                                {link.icon || defaultIcon}
                              </div>

                              {/* Main content */}
                              <div className="flex-grow-1 d-flex flex-column" style={{ lineHeight: '1.2' }}>
                                {/* Title */}
                                <div className="fw-bold">
                                  {link.title}
                                </div>

                                {/* Description (optional) */}
                                {link.description && (
                                  <div className="text-muted small">
                                    {link.description}
                                  </div>
                                )}
                              </div>

                              {/* URL vertically centered */}
                              <div
                                className="text-primary small ms-2"
                                style={{
                                  position: 'absolute',
                                  right: '0',
                                  top: '50%',
                                  transform: 'translateY(-50%)',
                                  opacity: 0.7
                                }}
                              >
                                {link.path}
                              </div>
                            </div>
                          </Link>
                        ))}
                      </div>
                    </div>
                  )
                )}

                {!Object.values(groupedLinks).some(group => group.length > 0) && (
                  <div className="p-3 text-center text-muted">
                    No se encontraron resultados para "{searchTerm}"
                  </div>
                )}
              </SimpleBar>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DdSearchbar;
