// Importar imágenes de logos
//import logoDarkIcon from '../assets/images/logos/logo-dark-icon.svg';
//import logoLightIcon from '../assets/images/logos/logo-light-icon.svg';
//import logoDarkText from '../assets/images/logos/logo-dark-text.svg';
//import logoLightText from '../assets/images/logos/logo-light-text.svg';

import raycoLightLogo from '../assets/images/rayco/raycoLightLogo.png';
import raycoDarkLogo from '../assets/images/rayco/raycoDarkLogo.png';
import raycoSwooshLight from '../assets/images/rayco/NSwooshBlanco.png';
import raycoSwooshDark from '../assets/images/rayco/NSwooshNegro.png';
import raycoSwooshBlanco from '../assets/images/rayco/nikeSwooshBlanco.png';
import raycoSwooshBNikeNegro from '../assets/images/rayco/NikeNegro.png';
import raycoMercosurLogo from '../assets/images/rayco/mercosurLogo.bmp';
import raycoAdidasBarDark from '../assets/images/rayco/adidasBarDark.bmp';
import profileBg from '../assets/images/backgrounds/profile-bg.jpg';
import userInfoBg from '../assets/images/backgrounds/user-info.jpg';
import flagEn from '../assets/images/svgs/icon-flag-en.svg';
import flagEs from '../assets/images/svgs/icon-flag-es.svg';
import googleIcon from '../assets/images/svgs/google-icon.svg';
import facebookIcon from '../assets/images/svgs/facebook-icon.svg';

export const importedAssets = {
  // Logos
  logos: {
    icon: {
      dark: raycoSwooshDark,
      light: raycoSwooshLight,
    },
    text: {
      dark: raycoDarkLogo,
      light: raycoSwooshBlanco,
    }
  },
  // Imágenes de perfil por defecto
  profiles: {
    default: raycoMercosurLogo,
  },
  // Iconos de banderas
  flags: {
    en: flagEn,
    es: flagEs,
  },
  // Fondos
  backgrounds: {
    login: raycoMercosurLogo,
    profile: profileBg,
    userInfo: userInfoBg
  },
  // Iconos de redes sociales
  socialIcons: {
    google: googleIcon,
    facebook: facebookIcon
  },
  rayco:{
    lightIcon: raycoLightLogo,
    darkIcon: raycoDarkLogo,
    swooshLight: raycoSwooshLight,
    swooshDark: raycoSwooshDark,
    mercosurLogo: raycoMercosurLogo,
    adidasBarDark: raycoAdidasBarDark,
    swooshBNikeNegro: raycoSwooshBNikeNegro,
    swooshBlanco: raycoSwooshBlanco,
  }
};

export const getAssetPath = (path: string): string => {
  if (path.startsWith('http')) {
    return path;
  }

  if (path.startsWith('/')) {
    return path;
  }
  return path;
};

export const getLogoPath = (type: 'icon' | 'text', theme: 'dark' | 'light' = 'light'): string => {
  return importedAssets.logos[type][theme];
};

export const getLoginLogoPath = (type: 'icon' | 'text'): string => {
  if (type === 'icon') {
    return importedAssets.rayco.swooshDark; // Logo icon para login (fondo blanco)
  } else {
    return importedAssets.rayco.darkIcon; // Logo text para login (fondo blanco)
  }
};

export const getDefaultProfileImage = (): string => {
  return importedAssets.profiles.default;
};

export const getFlagPath = (lang: 'en' | 'es'): string => {
  return importedAssets.flags[lang];
};

export const getBackgroundPath = (type: 'profile' | 'userInfo'): string => {
  return importedAssets.backgrounds[type];
};

export const getSocialIconPath = (type: 'google' | 'facebook'): string => {
  return importedAssets.socialIcons[type];
};

export default {
  getAssetPath,
  getLogoPath,
  getDefaultProfileImage,
  getFlagPath,
  getBackgroundPath,
  getSocialIconPath,
  importedAssets
};
