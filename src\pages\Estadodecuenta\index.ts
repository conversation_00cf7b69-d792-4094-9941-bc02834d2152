
import EstadoDeCuentaComponent from './Estadodecuenta.tsx';
import withPageMetadata from '@/hoc/withPageMetadata';
import { registerPageModule } from '@/routes/PageMetadata';
const WrappedEstadoDeCuenta = withPageMetadata(EstadoDeCuentaComponent);
if (EstadoDeCuentaComponent.pageMetadata) {
  try {
    registerPageModule('EstadoDeCuenta', EstadoDeCuentaComponent, {
      ...EstadoDeCuentaComponent.pageMetadata,
      path: EstadoDeCuentaComponent.pageMetadata.path || '/main/estadodecuenta'
    });
  } catch (error) { }
}
export const pageMetadata = EstadoDeCuentaComponent.pageMetadata;
export default WrappedEstadoDeCuenta;
