export const MAIN_ROUTE_PREFIX = import.meta.env.VITE_MAIN_ROUTE_PREFIX || '/main';
export const AUTH_ROUTE_PREFIX = '/auth';
export const ROUTES = {
  LOGIN: `${AUTH_ROUTE_PREFIX}/login`,
  REGISTER: `${AUTH_ROUTE_PREFIX}/register`,
  FORGOT_PASSWORD: `${AUTH_ROUTE_PREFIX}/forgot-password`,
  RESET_PASSWORD: `${AUTH_ROUTE_PREFIX}/reset-password`,
  ERROR: `${AUTH_ROUTE_PREFIX}/error`,
  UNAUTHORIZED: `${AUTH_ROUTE_PREFIX}/unauthorized`,
  DASHBOARD: `${MAIN_ROUTE_PREFIX}/dashboard`,
  HOME: `${MAIN_ROUTE_PREFIX}/dashboard`
};

export default {
  MAIN_ROUTE_PREFIX,
  AUTH_ROUTE_PREFIX,
  ROUTES
};
