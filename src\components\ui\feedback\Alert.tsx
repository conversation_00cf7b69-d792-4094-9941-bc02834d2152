import React, { useState } from 'react';
import classNames from 'classnames';
import { FA6 } from '../../../config/icons/iconUtils';
export type AlertType = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
export interface AlertProps {
  type?: AlertType;
  title?: string;
  message: React.ReactNode;
  dismissible?: boolean;
  showIcon?: boolean;
  className?: string;
  onClose?: () => void;
  border?: boolean;
  subtle?: boolean;
  small?: boolean;
}

const Alert: React.FC<AlertProps> = ({
  type = 'primary',
  title,
  message,
  dismissible = false,
  showIcon = true,
  className,
  onClose,
  border = false,
  subtle = false,
  small = false,
}) => {
  const [show, setShow] = useState(true);
  const handleClose = () => {
    setShow(false);
    if (onClose) {
      onClose();
    }
  };

  if (!show) {
    return null;
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <FA6.FaCircleCheck />;
      case 'danger':
        return <FA6.FaCircleExclamation />;
      case 'warning':
        return <FA6.FaTriangleExclamation />;
      case 'info':
        return <FA6.FaCircleInfo />;
      default:
        return <FA6.FaCircleInfo />;
    }
  };

  return (
    <div
      className={classNames(
        'alert',
        subtle ? `alert-${type}-subtle` : `alert-${type}`,
        dismissible ? 'alert-dismissible' : '',
        {
          'border': border,
          'p-2': small,
          [`border-${type}`]: border
        },
        className
      )}
      role="alert"
    >
      <div className="d-flex align-items-start">
        {showIcon && (
          <div className="me-3">
            {getIcon()}
          </div>
        )}
        <div className="flex-grow-1">
          {title && (
            <div className={classNames(
              'alert-heading',
              small ? 'fs-6 mb-1' : 'h5'
            )}>
              {title}
            </div>
          )}
          <div className={small ? 'small' : ''}>
            {message}
          </div>
        </div>
        {dismissible && (
          <button
            type="button"
            className="btn-close"
            aria-label="Close"
            onClick={handleClose}
          ></button>
        )}
      </div>
    </div>
  );
};

export default Alert;
