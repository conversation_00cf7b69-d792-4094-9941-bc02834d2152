import React, { useState, useEffect } from 'react';
import { useTheme, useLayout } from '../../context';
import { Outlet } from 'react-router-dom';
import { Sidebar as VerticalSidebar, HorizontalSidebar } from '../layout/sidebar';
import Header from './header/Header';
import Breadcrumb from './Breadcrumb';
import '@/assets/scss/styles.scss';
import { useSidebar } from '../../hooks';
import Footer from './Footer';

const Layout: React.FC = () => {
  const { settings: themeSettings, getLogoPath } = useTheme();
  const { settings: layoutSettings, layout, windowWidth, breakpointValues } = useLayout();
  const { handleSidebarResponsive } = useSidebar();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <div
      id="main-wrapper"
      data-layout={layoutSettings.layout}
      data-theme={themeSettings.theme}
      data-sidebartype={layoutSettings.sidebarType}
      data-boxed-layout={layoutSettings.boxedLayout ? "boxed" : "full"}
      className={layoutSettings.sidebarVisible ? 'show-sidebar' : ''}
    >
      {isLoading && (
        <div className="preloader">
          <img src={getLogoPath('icon')} alt="loader" className="lds-ripple img-fluid" width="35" />
        </div>
      )}

      {!layout.isHorizontal && (
        <aside
          className="left-sidebar with-vertical"
          style={{
            backgroundColor: themeSettings.theme === 'dark' ? '#152332' : 'white'
          }}
        >
          <div
            className="scrollbar"
            style={{
              backgroundColor: themeSettings.theme === 'dark' ? '#152332' : 'white'
            }}
          >
            <VerticalSidebar show={true} />
          </div>
        </aside>
      )}

      <div className={layout.isHorizontal ? "app-header with-horizontal" : "with-vertical"}>
        <Header toggleSidebar={handleSidebarResponsive} />
      </div>

      {layout.isHorizontal && (
        <aside
          className={`left-sidebar with-horizontal ${layoutSettings.sidebarVisible ? 'show' : ''}`}
          style={{
            backgroundColor: themeSettings.theme === 'dark' ? '#152332' : 'white'
          }}
        >
          <HorizontalSidebar />
        </aside>
      )}

      <div className="page-wrapper">
        <div className="body-wrapper">
          <div className={layoutSettings.boxedLayout ? "container" : "container-fluid"}>
            <Breadcrumb />
            <Outlet />
            <Footer />
          </div>
        </div>
      </div>

      {layoutSettings.sidebarVisible && windowWidth < breakpointValues.xl && (
        <div
          className="dark-transparent sidebartoggler"
          onClick={handleSidebarResponsive}
          aria-hidden="true"
        ></div>
      )}
    </div>
  );
};

export default Layout;
