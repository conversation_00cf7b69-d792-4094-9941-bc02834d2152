import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import type { PreRenderedAsset } from 'rollup';
import path from 'path';
import { fileURLToPath } from 'url';


const __dirname = path.dirname(fileURLToPath(import.meta.url))

const aliases = {
  '@': path.resolve(__dirname, './src'),
  '@assets': path.resolve(__dirname, './src/assets'),
  '@config': path.resolve(__dirname, './src/config'),
  '@context': path.resolve(__dirname, './src/context'),
  '@components': path.resolve(__dirname, './src/components'),
  '@pages': path.resolve(__dirname, './src/pages'),
  '@routes': path.resolve(__dirname, './src/routes'),
  '@utils': path.resolve(__dirname, './src/utils'),
  '@hoc': path.resolve(__dirname, './src/hoc'),
  '@services': path.resolve(__dirname, './src/services'),
  '@api': path.resolve(__dirname, './src/api'),
  '@hooks': path.resolve(__dirname, './src/hooks'),
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  const useDocker = env.VITE_USE_DOCKER === 'true'
  console.log('Entorno Docker:', useDocker + '\n')

  let sqlBackendUrl, pgBackendUrl

  if (useDocker) {
    sqlBackendUrl = env.VITE_BACKEND_SQL_URL_DOCKER || 'http://apibackendsqlserver:8080'
    pgBackendUrl = env.VITE_BACKEND_PG_URL_DOCKER || 'http://apibackendpostgresql:8010'
  } else {
    sqlBackendUrl = env.VITE_BACKEND_SQL_URL_COMPLETE || 'http://**************:8080'
    pgBackendUrl = env.VITE_BACKEND_PG_URL_COMPLETE || 'http://**************:8010'
  }

  console.log('SQL Backend URL:', sqlBackendUrl)
  console.log('PG Backend URL:', pgBackendUrl)

  const port = parseInt(env.VITE_APP_PORT) || 3000

  return {
    plugins: [react()],
    server: {
      https:{
        key: './certs/key.pem',
        cert: './certs/cert.pem'
      },
      port: port,
      host: true,
      allowedHosts: [
        'localhost',
        '**************',
        '127.0.0.1',
        '.ngrok.io',
        '.ngrok-free.app',
        '.ngrok.app'
      ],
      proxy: {
        '/back': {
          target: pgBackendUrl.replace('/back/handle', ''),
          changeOrigin: true,
          secure: false,
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              //console.log('Proxy error for /back:', err);
            });
            proxy.on('proxyReq', (proxyReq, _req, _res) => {
              //console.log('Proxying /back request to:', proxyReq.getHeader('host') + proxyReq.path);
            });
          }
        },
        '/ws': {
          target: sqlBackendUrl.replace('/ws/handle', ''),
          changeOrigin: true,
          secure: false,
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              //console.log('Proxy error for /ws:', err);
            });
          }
        },
        '/api': {
          target: sqlBackendUrl.replace('/ws/handle', ''),
          changeOrigin: true,
          secure: false,
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              //console.log('Proxy error for /api:', err);
            });
          }
        }
      },
      cors: true
    },
    preview: {
      port: port,
      host: true,
      cors: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, Content-Length, X-Requested-With'
      }
    },
    resolve: {
      alias: aliases,
    },
    css: {
      preprocessorOptions: {
        scss: {
          quietDeps: true,
          verbose: false,
          logger: {
            warn: () => {},
            debug: () => {}
          },
          additionalData: `
            @import "@assets/scss/variables/variables";
            @import "@assets/scss/variables/theme_variables";
          `,
        },
      },
    },
    build: {
      assetsDir: 'assets',
      rollupOptions: {
        output: {
          assetFileNames: (assetInfo: PreRenderedAsset) => {
            const extType = assetInfo.names?.[0]?.split('.').at(1) || 'other';
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
              return 'assets/img/[name]-[hash][extname]';
            }
            return `assets/${extType}/[name]-[hash][extname]`;
          },
        },
      },
    },
  }
})
