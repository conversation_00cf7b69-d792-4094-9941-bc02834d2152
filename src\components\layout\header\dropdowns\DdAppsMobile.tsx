import React, { useEffect, useState, useRef } from 'react';
import { useLayout, useTheme } from '@/context';
import { FA6 } from '@/config/icons/iconUtils';
import { getRoutesForUser, buildNavigationFromRoutes, NavigationItem } from '@/routes/PageMetadata';
import { menuStructure, getPageSectionInfo } from '@/hoc/withPageMetadata';
import { Link } from 'react-router-dom';

interface DdAppsMobileProps {
  isOpen: boolean;
  onClose: () => void;
}

const DdAppsMobile: React.FC<DdAppsMobileProps> = ({ isOpen, onClose }) => {
  const { breakpoints } = useLayout();
  const { settings, resources } = useTheme();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [animationState, setAnimationState] = useState<'closed' | 'opening' | 'open' | 'closing'>('closed');
  const offcanvasRef = useRef<HTMLDivElement>(null);

  const routes = getRoutesForUser();
  const navigationItems = buildNavigationFromRoutes(routes);

  useEffect(() => {
    let animationTimer: NodeJS.Timeout;

    if (isOpen && animationState === 'closed') {
      setAnimationState('opening');
      animationTimer = setTimeout(() => {
        setAnimationState('open');
      }, 10); // Short delay to trigger CSS transition
    } else if (!isOpen && (animationState === 'open' || animationState === 'opening')) {
      setAnimationState('closing');
      animationTimer = setTimeout(() => {
        setAnimationState('closed');
      }, 300); // Match Bootstrap's default transition time
    }

    return () => {
      if (animationTimer) {
        clearTimeout(animationTimer);
      }
    };
  }, [isOpen, animationState]);

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('offcanvas-open');

      const styleElement = document.createElement('style');
      styleElement.id = 'offcanvas-style-fix';
      styleElement.innerHTML = `
        body.offcanvas-open {
          overflow: auto !important;
          padding-right: 0 !important;
        }
        .offcanvas {
          transition: transform 0.3s ease-in-out;
        }
        .offcanvas.collapsing {
          visibility: visible;
          height: auto;
          transition: transform 0.3s ease-in-out;
        }
      `;
      document.head.appendChild(styleElement);
    } else {
      document.body.classList.remove('offcanvas-open');
      const styleElement = document.getElementById('offcanvas-style-fix');
      if (styleElement) {
        styleElement.remove();
      }
    }

    return () => {
      document.body.classList.remove('offcanvas-open');
      const styleElement = document.getElementById('offcanvas-style-fix');
      if (styleElement) {
        styleElement.remove();
      }
    };
  }, [isOpen]);

  useEffect(() => {
    if (!breakpoints.isMobile && !breakpoints.isTablet && isOpen) {
      onClose();
    }
  }, [breakpoints.isMobile, breakpoints.isTablet, isOpen, onClose]);

  if (animationState === 'closed' && !isOpen && !breakpoints.isMobile && !breakpoints.isTablet) {
    return null;
  }

  const toggleExpandItem = (key: string, e: React.MouseEvent) => {
    e.preventDefault();
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(key)) {
        newSet.delete(key);
      } else {
        newSet.add(key);
      }
      return newSet;
    });
  };

  const isItemExpanded = (key: string) => expandedItems.has(key);

  const renderNavItem = (item: NavigationItem) => {
    const hasChildren = item.children && item.children.length > 0;
    const isItemActive = window.location.pathname === item.path;
    const isExpanded = isItemExpanded(item.key);

    return (
      <li className="sidebar-item" key={item.key}>
        {hasChildren ? (
          <>
            <a
              className={`sidebar-link has-arrow px-1 ${isExpanded ? 'active' : ''} ${isItemActive ? 'active' : ''}`}
              href="#"
              onClick={(e) => toggleExpandItem(item.key, e)}
              aria-expanded={isExpanded}
            >
              <span className="d-flex">
                {item.icon || <FA6.FaCircle className="fs-6" />}
              </span>
              <span className="hide-menu">{item.title}</span>
            </a>
            <ul
              aria-expanded={isExpanded}
              className={`collapse first-level ${isExpanded ? 'show' : ''}`}
            >
              {item.children?.map(child => renderNavItem(child))}
            </ul>
          </>
        ) : (
          <Link
            to={item.path}
            className={`sidebar-link px-1 ${isItemActive ? 'active' : ''}`}
            onClick={onClose}
            aria-expanded="false"
          >
            <span className="d-flex">
              {item.icon || <FA6.FaCircle className="fs-6" />}
            </span>
            <span className="hide-menu">{item.title}</span>
          </Link>
        )}
      </li>
    );
  };

  const renderAppsMenu = () => (
    <li className="sidebar-item">
      <a
        className={`sidebar-link has-arrow px-1 ${isItemExpanded('apps') ? 'active' : ''}`}
        href="#"
        onClick={(e) => toggleExpandItem('apps', e)}
        aria-expanded={isItemExpanded('apps')}
      >
        <span className="d-flex">
          <FA6.FaShieldHeart className="fs-6" />
        </span>
        <span className="hide-menu">Apps</span>
      </a>
      <ul
        aria-expanded={isItemExpanded('apps')}
        className={`collapse first-level my-3 ${isItemExpanded('apps') ? 'show' : ''}`}
      >
        <li className="sidebar-item py-2">
          <a href="#" className="d-flex align-items-center position-relative">
            <div className="bg-primary-subtle rounded-circle round-40 me-3 p-6 d-flex align-items-center justify-content-center">
              <FA6.FaComment className="text-primary fs-5" />
            </div>
            <div className="d-inline-block">
              <h6 className="mb-0">Chat Application</h6>
              <span className="fs-3 d-block text-muted">New messages arrived</span>
            </div>
          </a>
        </li>
        <li className="sidebar-item py-2">
          <a href="#" className="d-flex align-items-center position-relative">
            <div className="bg-secondary-subtle rounded-circle round-40 me-3 p-6 d-flex align-items-center justify-content-center">
              <FA6.FaFileInvoice className="text-secondary fs-5" />
            </div>
            <div className="d-inline-block">
              <h6 className="mb-0">Invoice App</h6>
              <span className="fs-3 d-block text-muted">Get latest invoice</span>
            </div>
          </a>
        </li>
        <li className="sidebar-item py-2">
          <a href="#" className="d-flex align-items-center position-relative">
            <div className="bg-success-subtle rounded-circle round-40 me-3 p-6 d-flex align-items-center justify-content-center">
              <FA6.FaAddressBook className="text-success fs-5" />
            </div>
            <div className="d-inline-block">
              <h6 className="mb-0">Contact Application</h6>
              <span className="fs-3 d-block text-muted">2 Unsaved Contacts</span>
            </div>
          </a>
        </li>
        <li className="sidebar-item py-2">
          <a href="#" className="d-flex align-items-center position-relative">
            <div className="bg-warning-subtle rounded-circle round-40 me-3 p-6 d-flex align-items-center justify-content-center">
              <FA6.FaEnvelopeOpen className="text-warning fs-5" />
            </div>
            <div className="d-inline-block">
              <h6 className="mb-0">Email App</h6>
              <span className="fs-3 d-block text-muted">Get new emails</span>
            </div>
          </a>
        </li>
        <li className="sidebar-item py-2">
          <a href="#" className="d-flex align-items-center position-relative">
            <div className="bg-danger-subtle rounded-circle round-40 me-3 p-6 d-flex align-items-center justify-content-center">
              <FA6.FaCartShopping className="text-danger fs-5" />
            </div>
            <div className="d-inline-block">
              <h6 className="mb-0">User Profile</h6>
              <span className="fs-3 d-block text-muted">Learn more information</span>
            </div>
          </a>
        </li>
        <li className="sidebar-item py-2">
          <a href="#" className="d-flex align-items-center position-relative">
            <div className="bg-primary-subtle rounded-circle round-40 me-3 p-6 d-flex align-items-center justify-content-center">
              <FA6.FaCalendar className="text-primary fs-5" />
            </div>
            <div className="d-inline-block">
              <h6 className="mb-0">Calendar App</h6>
              <span className="fs-3 d-block text-muted">Get dates</span>
            </div>
          </a>
        </li>
        <ul className="px-8 mt-7 mb-4">
          <li className="sidebar-item mb-3">
            <h5 className="fs-5 fw-semibold">Quick Links</h5>
          </li>
          <li className="sidebar-item py-2">
            <a className="fs-3" href="#">Pricing Page</a>
          </li>
          <li className="sidebar-item py-2">
            <a className="fs-3" href="#">Authentication Design</a>
          </li>
          <li className="sidebar-item py-2">
            <a className="fs-3" href="#">Register Now</a>
          </li>
          <li className="sidebar-item py-2">
            <a className="fs-3" href="#">404 Error Page</a>
          </li>
          <li className="sidebar-item py-2">
            <a className="fs-3" href="#">Notes App</a>
          </li>
          <li className="sidebar-item py-2">
            <a className="fs-3" href="#">User Application</a>
          </li>
          <li className="sidebar-item py-2">
            <a className="fs-3" href="#">Account Settings</a>
          </li>
        </ul>
      </ul>
    </li>
  );

  const showBackdrop = animationState === 'opening' || animationState === 'open';

  let offcanvasClassName = 'offcanvas offcanvas-end';

  if (animationState === 'opening' || animationState === 'closing') {
    offcanvasClassName += ' collapsing';
  } else if (animationState === 'open') {
    offcanvasClassName += ' show';
  }

  return (
    <>
      {showBackdrop && (
        <div
          className={`offcanvas-backdrop fade ${animationState === 'open' ? 'show' : ''}`}
          onClick={(e) => {
            e.stopPropagation();
            onClose();
          }}
        ></div>
      )}

      <div
        ref={offcanvasRef}
        className={offcanvasClassName}
        style={{
          width: '52%',
          maxWidth: '100%',
          position: 'fixed',
          zIndex: 1045,
          transform: animationState === 'open' ? 'none' : 'translateX(100%)',
          visibility: animationState === 'closed' ? 'hidden' : 'visible'
        }}
        tabIndex={-1}
        id="mobilenavbar"
        aria-labelledby="offcanvasWithBothOptionsLabel"
      >
        <nav className="sidebar-nav scroll-sidebar">
          <div className="offcanvas-header justify-content-between">
            <a href="/" className="text-nowrap logo-img d-flex align-items-center">
              <b className="logo-icon">
                <img
                  src={resources.getAssetPath('/images/logos/logo-icon.svg')}
                  alt="homepage"
                  className={settings.theme === 'light' ? 'dark-logo' : 'light-logo'}
                  width="35"
                />
              </b>
              <span className="logo-text">
                <img
                  src={resources.getAssetPath('/images/logos/logo-text.svg')}
                  alt="homepage"
                  className={`${settings.theme === 'light' ? 'dark-logo' : 'light-logo'} ps-2`}
                  width="140"
                />
              </span>
            </a>
            <button
              type="button"
              className="btn-close"
              onClick={onClose}
              aria-label="Close"
            ></button>
          </div>

          <div className="offcanvas-body h-n80" data-simplebar>
            <ul id="sidebarnav">
              {renderAppsMenu()}

              {(() => {
                const sections: string[] = [];
                const sectionItems: Record<string, NavigationItem[]> = {};

                Object.keys(menuStructure).forEach(section => {
                  sections.push(section);
                  sectionItems[section] = [];
                });

                navigationItems.forEach(item => {
                  const { section } = getPageSectionInfo(item.key);

                  if (!sectionItems[section]) {
                    sectionItems[section] = [];
                  }

                  sectionItems[section].push(item);
                });

                return sections.flatMap(section => {
                  if (sectionItems[section] && sectionItems[section].length > 0) {
                    const sectionElements = [(
                      <li className="nav-small-cap" key={section}>
                        <FA6.FaEllipsis className="nav-small-cap-icon fs-4" />
                        <span className="hide-menu">{section.toLowerCase()}</span>
                      </li>
                    )];

                    sectionItems[section].forEach(item => {
                      sectionElements.push(renderNavItem(item));
                    });

                    return sectionElements;
                  }
                  return [];
                });
              })()}
            </ul>
          </div>
        </nav>
      </div>
    </>
  );
};

export default DdAppsMobile;
