import { useContext } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { AuthContext } from '@/context/AuthContext';
import { ROUTES } from '@/routes/routeConstants';
import { getValidRoutes } from '@/routes/PageMetadata';
import { processNavigationUrl } from '@/utils/urlCleaner';

export const RouteMiddleware = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const location = useLocation();

  const fullUrl = window.location.href;
  const currentPath = location.pathname;

  const urlProcessResult = processNavigationUrl(fullUrl, currentPath, isAuthenticated);

  if (urlProcessResult.shouldRedirect && urlProcessResult.redirectTo) {
    return <Navigate to={urlProcessResult.redirectTo} replace />;
  }

  if (location.pathname === "/") {
    if (isAuthenticated) {
      return <Navigate to={ROUTES.HOME} replace />;
    } else {
      return <Navigate to={ROUTES.LOGIN} state={{ from: location }} replace />;
    }
  }

  // Only proceed with route validation if URL is not malformed
  const validRoutes = getValidRoutes();
  const isValidRoute = validRoutes.includes(currentPath) || currentPath === "/";

  if (!isValidRoute) {
    return <Navigate to={ROUTES.HOME} />;
  }

  if (!isAuthenticated) {
    localStorage.removeItem('auth_token');
    sessionStorage.clear();
    return <Navigate to={ROUTES.LOGIN} state={{ from: location }} replace />;
  }

  return <Outlet />;
};

export default RouteMiddleware;
