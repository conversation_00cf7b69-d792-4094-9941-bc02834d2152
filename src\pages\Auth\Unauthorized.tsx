import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { PageMetadata } from '../../hoc/withPageMetadata';
import errorImg from '../../assets/images/backgrounds/errorimg.svg';
import { AUTH_CONFIG, PERMISSIONS } from '../../config/authConfig';

const Unauthorized: React.FC & { pageMetadata: PageMetadata } = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [countdown, setCountdown] = useState(5);

  const getPreviousPage = useCallback(() => {
    console.log(`[UNAUTH] Getting previous page`);

    if (location.state?.from?.pathname) {
      console.log(`[UNAUTH] Found previous page in location state: ${location.state.from.pathname}`);
      return location.state.from.pathname;
    }

    if (window.history.length > 1) {
      console.log(`[UNAUTH] Using home path as fallback: ${AUTH_CONFIG.homePath}`);
      return AUTH_CONFIG.homePath;
    }

    console.log(`[UNAUTH] No previous page found, using home path: ${AUTH_CONFIG.homePath}`);
    return AUTH_CONFIG.homePath;
  }, [location.state]);

  const handleRedirect = useCallback(() => {
    const previousPage = getPreviousPage();
    console.log(`[UNAUTH] Redirecting to: ${previousPage}`);
    navigate(previousPage, { replace: true });
  }, [navigate, getPreviousPage]);

  useEffect(() => {
    let isActive = true;
    const timer = setInterval(() => {
      if (!isActive) {
        return;
      }
      setCountdown((prevCount) => {
        if (prevCount <= 1) {
          clearInterval(timer);
          setTimeout(() => {
            if (!isActive) {
              return;
            }
            handleRedirect();
          }, 0);

          return 0;
        }
        return prevCount - 1;
      });
    }, 1000);
    return () => {
      isActive = false;
      clearInterval(timer);
    };
  }, [handleRedirect]);

  return (
    <div className="position-relative overflow-hidden min-vh-100 w-100 d-flex align-items-center justify-content-center">
      <div className="d-flex align-items-center justify-content-center w-100">
        <div className="row justify-content-center w-100">
          <div className="col-lg-4">
            <div className="text-center">
              <img src={errorImg} alt="Acceso denegado" className="img-fluid" width="500" />
              <h1 className="fw-semibold mb-7 fs-9">¡Oops!</h1>
              <h4 className="fw-semibold mb-7">No tienes permisos para acceder a esta página.</h4>
              <p className="mb-3">Serás redirigido en <span className="fw-bold text-primary">{countdown}</span> segundos...</p>
              <button
                className="btn btn-primary"
                onClick={handleRedirect}
                role="button"
              >
                Volver ahora
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

Unauthorized.pageMetadata = {
  title: 'Acceso Denegado',
  description: 'No tienes permisos para acceder a esta página',
  requiresAuth: false,
  path: '/auth/unauthorized',
  showInMenu: false,
  permissions: {
    requiredPermission: PERMISSIONS.NONE,
    resourceId: 'unauthorized'
  }
};

export default Unauthorized;
