export const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1300, 
  xxl: 1400
};

export type Breakpoint = keyof typeof BREAKPOINTS;

export const RESPONSIVE = {
  mobileBreakpoint: BREAKPOINTS.md,
  tabletBreakpoint: BREAKPOINTS.lg,
  desktopBreakpoint: BREAKPOINTS.xl,
  sidebarCollapseBreakpoint: BREAKPOINTS.xl
};


export const DIMENSIONS = {
  headerHeight: 70,
  sidebar: {
    width: {
      full: 270,
      mini: 80
    }
  },

  boxed: {
    width: 1300,
    marginX: 50,
    sidebarMargin: 30
  },

  zIndex: {
    content: 10,
    dropdown: 20,
    overlay: 30,
    sidebar: 40,
    header: 50,
    customizer: 99,
    toast: 999,
    modal: 1050,
    popover: 1060,
    tooltip: 1070,
    dosmil: 2000,
    tresmil: 3000,
    fullscreen: 9998,
    overnine:9999,
  },
  
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48
  }
};

export const responsiveUtils = {

  isGreaterThan: (width: number, breakpoint: Breakpoint): boolean => {
    return width >= BREAKPOINTS[breakpoint];
  },

  isLessThan: (width: number, breakpoint: Breakpoint): boolean => {
    return width < BREAKPOINTS[breakpoint];
  },


  isBetween: (width: number, minBreakpoint: Breakpoint, maxBreakpoint: Breakpoint): boolean => {
    return width >= BREAKPOINTS[minBreakpoint] && width < BREAKPOINTS[maxBreakpoint];
  },


  getBreakpointInfo: (width: number) => ({
    isMobile: width < BREAKPOINTS.md,
    isTablet: width >= BREAKPOINTS.md && width < BREAKPOINTS.xl,
    isDesktop: width >= BREAKPOINTS.xl,
    isXs: width < BREAKPOINTS.sm,
    isSm: width >= BREAKPOINTS.sm && width < BREAKPOINTS.md,
    isMd: width >= BREAKPOINTS.md && width < BREAKPOINTS.lg,
    isLg: width >= BREAKPOINTS.lg && width < BREAKPOINTS.xl,
    isXl: width >= BREAKPOINTS.xl && width < BREAKPOINTS.xxl,
    isXxl: width >= BREAKPOINTS.xxl
  })
};


export default {
  BREAKPOINTS,
  RESPONSIVE,
  DIMENSIONS,
  responsiveUtils
};
