import DashboardComponent from './Dashboard.tsx';
import withPageMetadata from '../../hoc/withPageMetadata';
import { registerPageModule } from '../../routes/PageMetadata';

const Dashboard = withPageMetadata(DashboardComponent);
if (DashboardComponent.pageMetadata) {
  try {
    registerPageModule('Dashboard', DashboardComponent, {
      ...DashboardComponent.pageMetadata,
      path: DashboardComponent.pageMetadata.path || '/main/dashboard'
    });
  } catch (error) {
    console.error('[DASHBOARD] Error registering Dashboard page module:', error);
  }
}
export const pageMetadata = DashboardComponent.pageMetadata;

export default Dashboard;
