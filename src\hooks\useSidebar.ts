import { useCallback } from 'react';
import { useLayout } from '@/context';
import { RESPONSIVE } from '@/config';

export const useSidebar = () => {
  const {
    settings,
    updateSettings,
    windowWidth
  } = useLayout();

  const toggleSidebarType = useCallback(() => {
    const currentType = document.body.getAttribute('data-sidebartype') || 'full';
    const newType = currentType === 'full' ? 'mini-sidebar' : 'full';
    document.body.setAttribute('data-sidebartype', newType);
    const originalAddEventListener = window.addEventListener;
    window.addEventListener = function(
      type: string,
      listener: EventListenerOrEventListenerObject,
      options?: boolean | AddEventListenerOptions
    ) {
      if (type === 'resize') {
        return;
      }
      return originalAddEventListener.call(window, type, listener, options);
    };
    updateSettings({ sidebarType: newType });
    setTimeout(() => {
      window.addEventListener = originalAddEventListener;
    }, 500);
  }, [updateSettings]);

  const toggleSidebarVisibility = useCallback(() => {
    const mainWrapper = document.getElementById('main-wrapper');
    if (mainWrapper) {
      const isCurrentlyVisible = mainWrapper.classList.contains('show-sidebar');
      if (isCurrentlyVisible) {
        mainWrapper.classList.remove('show-sidebar');
      } else {
        mainWrapper.classList.add('show-sidebar');
      }
      const newVisibility = !isCurrentlyVisible;
      updateSettings({ sidebarVisible: newVisibility });
      console.log(`Toggled sidebar visibility to: ${newVisibility ? 'visible' : 'hidden'}`);
    }
  }, [updateSettings]);

  const handleSidebarResponsive = useCallback(() => {
    const sidebarBreakpoint = RESPONSIVE.sidebarCollapseBreakpoint;
    console.log(`handleSidebarResponsive: windowWidth=${windowWidth}, breakpoint=${sidebarBreakpoint}`);
    if (windowWidth < sidebarBreakpoint) {
      toggleSidebarVisibility();
    }
    else {
      toggleSidebarType();
    }
  }, [windowWidth, toggleSidebarVisibility, toggleSidebarType]);

  return {
    sidebarType: settings.sidebarType,
    sidebarVisible: settings.sidebarVisible,
    toggleSidebarType,
    toggleSidebarVisibility,
    handleSidebarResponsive
  };
};

export default useSidebar;
