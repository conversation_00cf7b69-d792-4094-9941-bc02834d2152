import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FA6 } from '@/config/icons/iconUtils';
import { routes, buildNavigationFromRoutes, NavigationItem } from '@/routes/PageMetadata';
import { useTheme } from '@/context/ThemeContext';
import { useLayout } from '@/context/LayoutContext';
import { useSidebar } from '@/hooks';
import { menuStructure, getPageSectionInfo } from '@/hoc/withPageMetadata';
import classNames from 'classnames';

const HorizontalSidebar: React.FC = () => {
  const location = useLocation();
  const { settings: themeSettings } = useTheme();
  const { windowWidth, breakpointValues } = useLayout();
  const { toggleSidebarVisibility } = useSidebar();
  const [menuItems, setMenuItems] = useState<NavigationItem[]>([]);
  const [openKeys, setOpenKeys] = useState<Record<string, boolean>>({});
  const [selectedItem, setSelectedItem] = useState<string | null>(null);

  useEffect(() => {
    const navigationItems = buildNavigationFromRoutes(routes);
    setMenuItems(navigationItems);
  }, []);

  useEffect(() => {
    const currentPath = location.pathname;

    const checkPath = (items: NavigationItem[]) => {
      items.forEach(item => {
        if (item.path === currentPath) {
          setSelectedItem(item.title);
        }
        if (item.children) {
          item.children.forEach(child => {
            if (child.path === currentPath) {
              setSelectedItem(child.title);
              setOpenKeys(prev => ({ ...prev, [item.title]: true }));
            }
          });
          checkPath(item.children);
        }
      });
    };

    checkPath(menuItems);
  }, [location.pathname, menuItems]);

  const toggleMenu = (key: string) => {
    setOpenKeys(prev => {
      const newState = { ...prev };
      newState[key] = !newState[key];
      return newState;
    });
  };

  const handleMenuClick = (item: NavigationItem) => {
    if (!item.children) {
      setSelectedItem(item.title);

      if (windowWidth < breakpointValues.xl) {
        toggleSidebarVisibility();
      }
    }
  };

  const renderNavItem = (item: NavigationItem) => {
    const hasChildren = item.children && item.children.length > 0;
    const isOpen = openKeys[item.title];
    const isActive = selectedItem === item.title;
    const isParentOfActive = item.children?.some(child => selectedItem === child.title);

    return (
      <li className={classNames("sidebar-item", {
        'selected': isParentOfActive,
        'active': isActive
      })} key={item.key}>
        {hasChildren ? (
          <>
            <a
              className={classNames("sidebar-link has-arrow waves-effect waves-dark", {
                'active': isParentOfActive || isActive
              })}
              href="javascript:void(0)"
              aria-expanded={isOpen}
              onClick={() => toggleMenu(item.title)}
            >
              {item.icon || <FA6.FaCircle className="aside-icon" />}
              <span className="hide-menu">{item.title}</span>
            </a>
            <ul aria-expanded={isOpen} className={classNames("collapse first-level", {
              'in': isOpen
            })}>
              {item.children!.map((child) => (
                <li className={classNames("sidebar-item", {
                  'active': selectedItem === child.title
                })} key={child.key}>
                  <Link
                    to={child.path}
                    className="sidebar-link"
                    onClick={() => handleMenuClick(child)}
                  >
                    <i className="ti ti-circle"></i>
                    <span className="hide-menu">{child.title}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </>
        ) : (
          <Link
            className={classNames("sidebar-link waves-effect waves-dark sidebar-link", {
              'active': isActive
            })}
            to={item.path}
            onClick={() => handleMenuClick(item)}
          >
            {item.icon || <FA6.FaCircle className="aside-icon" />}
            <span className="hide-menu">{item.title}</span>
          </Link>
        )}
      </li>
    );
  };

  const mobileClass = windowWidth < breakpointValues.xl ? 'mobile-sidebar' : '';

  const renderSectionItem = (title: string) => {
    return (
      <li className="nav-small-cap" key={title}>
        <FA6.FaEllipsis className="nav-small-cap-icon fs-4" />
        <span className="hide-menu">{title.toLowerCase()}</span>
      </li>
    );
  };

  const renderMenuItemsBySection = () => {
    const sections: string[] = [];
    const sectionItems: Record<string, NavigationItem[]> = {};

    Object.keys(menuStructure).forEach(section => {
      sections.push(section);
      sectionItems[section] = [];
    });

    menuItems.forEach(item => {
      const { section } = getPageSectionInfo(item.key);

      if (!sectionItems[section]) {
        sectionItems[section] = [];
      }

      sectionItems[section].push(item);
    });

    return sections.flatMap(section => {
      if (sectionItems[section] && sectionItems[section].length > 0) {
        const sectionElements = [renderSectionItem(section)];

        sectionItems[section].forEach(item => {
          sectionElements.push(renderNavItem(item));
        });

        return sectionElements;
      }
      return [];
    });
  };

  return (
    <div className={mobileClass} data-theme={themeSettings.theme}>
      <nav id="sidebarnavh" className="sidebar-nav scroll-sidebar container-fluid bg-white">
        <ul id="sidebarnav">
          {renderMenuItemsBySection()}
        </ul>
      </nav>
    </div>
  );
};

export default HorizontalSidebar;
