import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import { 
  ANIMATION_DURATION, 
  ANIMATION_CLASSES, 
  TIMING_FUNCTIONS, 
  COMPONENT_ANIMATIONS,
  animationUtils
} from '../config/animationConfig';

export interface AnimationContextType {
  animationsEnabled: boolean;
  durations: typeof ANIMATION_DURATION;
  classes: typeof ANIMATION_CLASSES;
  timingFunctions: typeof TIMING_FUNCTIONS;
  componentAnimations: typeof COMPONENT_ANIMATIONS;
  animateIn: (element: HTMLElement | null, animationClass?: string, duration?: number) => Promise<void>;
  animateOut: (element: HTMLElement | null, animationClass?: string, duration?: number) => Promise<void>;
  transition: (element: HTMLElement | null, properties: Record<string, string>, duration?: number, timingFunction?: string) => Promise<void>;
  smoothScrollTo: (target: HTMLElement | number, options?: { offset?: number; duration?: number; easing?: string; callback?: () => void; }) => Promise<void>;
  toggleAnimations: () => void;
  setAnimationsEnabled: (enabled: boolean) => void;
}

const defaultAnimationContext: AnimationContextType = {
  animationsEnabled: true,
  durations: ANIMATION_DURATION,
  classes: ANIMATION_CLASSES,
  timingFunctions: TIMING_FUNCTIONS,
  componentAnimations: COMPONENT_ANIMATIONS,
  animateIn: async () => {},
  animateOut: async () => {},
  transition: async () => {},
  smoothScrollTo: async () => {},
  toggleAnimations: () => {},
  setAnimationsEnabled: () => {}
};

export const AnimationContext = createContext<AnimationContextType>(defaultAnimationContext);
export const useAnimation = () => useContext(AnimationContext);

interface AnimationProviderProps {
  children: ReactNode;
  initialEnabled?: boolean;
}

export const AnimationProvider: React.FC<AnimationProviderProps> = ({ 
  children, 
  initialEnabled = true 
}) => {
  const [animationsEnabled, setAnimationsEnabled] = useState(initialEnabled);
  const toggleAnimations = useCallback(() => {
    setAnimationsEnabled(prev => !prev);
  }, []);
  
  const animateIn = useCallback((
    element: HTMLElement | null,
    animationClass: string = ANIMATION_CLASSES.fadeIn,
    duration: number = ANIMATION_DURATION.normal
  ): Promise<void> => {
    if (!animationsEnabled) return Promise.resolve();
    return animationUtils.animateIn(element, animationClass, duration);
  }, [animationsEnabled]);
  
  const animateOut = useCallback((
    element: HTMLElement | null,
    animationClass: string = ANIMATION_CLASSES.fadeOut,
    duration: number = ANIMATION_DURATION.normal
  ): Promise<void> => {
    if (!animationsEnabled) return Promise.resolve();
    return animationUtils.animateOut(element, animationClass, duration);
  }, [animationsEnabled]);
  
  const transition = useCallback((
    element: HTMLElement | null,
    properties: Record<string, string>,
    duration: number = ANIMATION_DURATION.normal,
    timingFunction: string = TIMING_FUNCTIONS.easeInOut
  ): Promise<void> => {
    if (!animationsEnabled) return Promise.resolve();
    return animationUtils.transition(element, properties, duration, timingFunction);
  }, [animationsEnabled]);

  const smoothScrollTo = useCallback((
    target: HTMLElement | number,
    options: { offset?: number; duration?: number; easing?: string; callback?: () => void; } = {}
  ): Promise<void> => {
    if (!animationsEnabled) {
      const scrollTop = typeof target === 'number'
        ? target
        : window.pageYOffset + target.getBoundingClientRect().top - (options.offset || 0);
      window.scrollTo({ top: Math.max(0, scrollTop) });
      options.callback?.();
      return Promise.resolve();
    }
    // Use longer default duration for more visible scrolling
    const defaultOptions = {
      duration: ANIMATION_DURATION.scroll,
      easing: TIMING_FUNCTIONS.easeOutCubic,
      ...options
    };
    return animationUtils.smoothScrollTo(target, defaultOptions);
  }, [animationsEnabled]);

  const contextValue: AnimationContextType = {
    animationsEnabled,
    durations: ANIMATION_DURATION,
    classes: ANIMATION_CLASSES,
    timingFunctions: TIMING_FUNCTIONS,
    componentAnimations: COMPONENT_ANIMATIONS,
    animateIn,
    animateOut,
    transition,
    smoothScrollTo,
    toggleAnimations,
    setAnimationsEnabled
  };
  
  return (
    <AnimationContext.Provider value={contextValue}>
      {children}
    </AnimationContext.Provider>
  );
};

export default AnimationProvider;
