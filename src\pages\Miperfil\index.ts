import MiperfilComponent from './Miperfil.tsx';
import withPageMetadata from '../../hoc/withPageMetadata';
import { registerPageModule } from '../../routes/PageMetadata';

const Miperfil = withPageMetadata(MiperfilComponent);
if (MiperfilComponent.pageMetadata) {
  try {
    registerPageModule('Miperfil', MiperfilComponent, {
      ...MiperfilComponent.pageMetadata,
      path: MiperfilComponent.pageMetadata.path || '/main/miperfil'
    });
  } catch (error) {
    console.error('[MIPERFIL] Error registering Miperfil page module:', error);
  }
}

export const pageMetadata = MiperfilComponent.pageMetadata;

export default Miperfil;