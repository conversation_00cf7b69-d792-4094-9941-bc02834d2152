/**
 * Menu Groups Configuration
 * 
 * This file provides a centralized way to configure menu groups/submenus
 * without creating dummy page files. Menu groups are containers that group
 * related pages together in the navigation menu.
 * 
 * Usage:
 * 1. Define your menu group here
 * 2. Set the `parent` property in your page's pageMetadata to match the group key
 * 3. The menu system will automatically create the submenu structure
 */

import { getIcon } from '@/config/icons/iconUtils';
import { MenuGroup, registerMenuGroup } from '@/routes/PageMetadata';

// Define menu groups
const menuGroupsConfig: Record<string, MenuGroup> = {
  'Persona': {
    key: 'Persona',
    title: 'Persona',
    icon: getIcon('FaUser', { className: 'fs-6' }),
    section: 'PERSONA',
    menuOrder: 2,
    showInMenu: true
  },
  // Add more menu groups here as needed
  // 'Reports': {
  //   key: 'Reports',
  //   title: 'Reportes',
  //   icon: getIcon('FaChartBar', { className: 'fs-6' }),
  //   section: 'REPORTS',
  //   menuOrder: 3,
  //   showInMenu: true
  // }
};

// Register all menu groups
export const initializeMenuGroups = (): void => {
  Object.values(menuGroupsConfig).forEach(group => {
    registerMenuGroup(group.key, group);
  });
};

export default menuGroupsConfig;
