import { useEffect, useState } from 'react';
import { UseApiOptions } from '@/api/useApi';
import { useAuth } from '@/context';
import { callProcedure } from '@/api/procedureService';

export interface AccountData {
  id: number;
  descripcion: string;
  vencido: string;
  aVencer: string;
  total: string;
  credito: string;
  creditoDisponible: string;
  moneda: string;
}

export interface TransactionData {
  id: number;
  fecha: string;
  tipoComprobante: string;
  nroComprobante: string;
  descripcion: string;
  ubicacion: string;
  monto: string;
  moneda: string;
}

export interface MovementApiResponse {
  mensaje: string;
  success: boolean;
  data: MovementData[];
}

export interface MovementData {
  TIPO_COMPROBANTE: string;
  CAMBIO: string;
  CAMBIO2: string;
  NROTRANS: number;
  FECHA: string;
  CUEPREFI: string;
  CODEMP: number;
  NOMBRE_EMPRESA: string;
  CODSUC: number;
  NOMBRE_SUCURSAL: string;
  PREFIJO: string;
  NUMERO: number;
  TOTAL_EN_DOLARES: string;
}

export interface ProcessedMovementGroup {
  companyCode: number;
  companyName: string;
  fbTotal: number;
  cbRcTotal: number;
  balance: number;
  totalAmount: number;
  movements: MovementData[];
}

export async function fetchMovementData(codCtaCte: string): Promise<MovementApiResponse> {
  try {
    // Validate parameters
    if (!codCtaCte) {
      console.error('Missing required parameter: codCtaCte');
      throw new Error('Missing required parameter: codCtaCte');
    }

    return await callProcedure<MovementApiResponse>('SP_GET_ESTADOCTE_X_CLIE', {
      '@CodCtaCte': codCtaCte
    }, 'sql');
  } catch (error) {
    console.error('Error fetching movement data:', error);
    throw error;
  }
}

export async function fetchCashMovementData(codCtaCte: string): Promise<MovementApiResponse> {
  try {
    // Validate parameters
    if (!codCtaCte) {
      console.error('Missing required parameter: codCtaCte');
      throw new Error('Missing required parameter: codCtaCte');
    }

    return await callProcedure<MovementApiResponse>('SP_GET_ESTADO_CONTADO_X_CLIE', {
      '@CodCtaCte': codCtaCte
    }, 'sql');
  } catch (error) {
    console.error('Error fetching cash movement data:', error);
    throw error;
  }
}

export function processMovementData(movements: MovementData[]): ProcessedMovementGroup[] {
  const groupedByCompany = movements.reduce((acc, movement) => {
    const companyCode = movement.CODEMP;
    if (!acc[companyCode]) {
      acc[companyCode] = {
        companyCode,
        companyName: movement.NOMBRE_EMPRESA,
        movements: []
      };
    }
    acc[companyCode].movements.push(movement);
    return acc;
  }, {} as Record<number, { companyCode: number; companyName: string; movements: MovementData[] }>);

  return Object.values(groupedByCompany).map(group => {
    const allMovements = group.movements;
    const matchedInvoices = new Map<string, boolean>();
    allMovements.forEach(movement => {
      if (movement.TIPO_COMPROBANTE === 'CB' || movement.TIPO_COMPROBANTE === 'RC') {
        const matchingFactura = allMovements.find(
          m => m.TIPO_COMPROBANTE === 'FB' &&
               m.PREFIJO === movement.PREFIJO &&
               m.NUMERO === movement.NUMERO &&
               !matchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`)
        );

        if (matchingFactura) {
          matchedInvoices.set(`${matchingFactura.PREFIJO}-${matchingFactura.NUMERO}`, true);
        }
      }
    });
    const fbTotal = allMovements
      .filter(m => m.TIPO_COMPROBANTE === 'FB' && !matchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`))
      .reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES), 0);
    const cbRcTotal = allMovements
      .filter(m => (m.TIPO_COMPROBANTE === 'CB' || m.TIPO_COMPROBANTE === 'RC'))
      .reduce((sum, m) => {
        const hasMatchingInvoice = allMovements.some(
          inv => inv.TIPO_COMPROBANTE === 'FB' &&
                inv.PREFIJO === m.PREFIJO &&
                inv.NUMERO === m.NUMERO
        );
        return hasMatchingInvoice ? sum : sum + parseFloat(m.TOTAL_EN_DOLARES);
      }, 0);
    const totalAmount = allMovements
      .reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES), 0);

    return {
      companyCode: group.companyCode,
      companyName: group.companyName,
      fbTotal,
      cbRcTotal,
      balance: fbTotal - cbRcTotal,
      totalAmount,
      movements: group.movements
    };
  });
}

export function processCashMovementData(movements: MovementData[]): {
  processedGroups: ProcessedMovementGroup[],
  currentMonthTotals: {
    fbTotal: number,
    cbRcTotal: number,
    balance: number
  }
} {

  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();

  let currentMonthFbTotal = 0;
  let currentMonthCbRcTotal = 0;

  const groupedByCompany = movements.reduce((acc, movement) => {
    const companyCode = movement.CODEMP;
    if (!acc[companyCode]) {
      acc[companyCode] = {
        companyCode,
        companyName: movement.NOMBRE_EMPRESA,
        movements: [],
        currentMonthMovements: []
      };
    }

    acc[companyCode].movements.push(movement);

    const movementDate = new Date(movement.FECHA);
    if (movementDate.getMonth() === currentMonth && movementDate.getFullYear() === currentYear) {
      acc[companyCode].currentMonthMovements.push(movement);
    }

    return acc;
  }, {} as Record<number, {
    companyCode: number;
    companyName: string;
    movements: MovementData[];
    currentMonthMovements: MovementData[];
  }>);

  const processedGroups = Object.values(groupedByCompany).map(group => {

    const allMovements = group.movements;
    const matchedInvoices = new Map<string, boolean>();

    allMovements.forEach(movement => {
      if (movement.TIPO_COMPROBANTE === 'CB' || movement.TIPO_COMPROBANTE === 'RC') {
        const matchingFactura = allMovements.find(
          m => m.TIPO_COMPROBANTE === 'FB' &&
               m.PREFIJO === movement.PREFIJO &&
               m.NUMERO === movement.NUMERO &&
               !matchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`)
        );

        if (matchingFactura) {
          matchedInvoices.set(`${matchingFactura.PREFIJO}-${matchingFactura.NUMERO}`, true);
        }
      }
    });
    const fbTotal = allMovements
      .filter(m => m.TIPO_COMPROBANTE === 'FB' && !matchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`))
      .reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES), 0);
    const cbRcTotal = allMovements
      .filter(m => (m.TIPO_COMPROBANTE === 'CB' || m.TIPO_COMPROBANTE === 'RC'))
      .reduce((sum, m) => {
        const hasMatchingInvoice = allMovements.some(
          inv => inv.TIPO_COMPROBANTE === 'FB' &&
                inv.PREFIJO === m.PREFIJO &&
                inv.NUMERO === m.NUMERO
        );
        return hasMatchingInvoice ? sum : sum + parseFloat(m.TOTAL_EN_DOLARES);
      }, 0);
    const totalAmount = allMovements
      .reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES), 0);
    const currentMonthMovements = group.currentMonthMovements;
    const currentMonthMatchedInvoices = new Map<string, boolean>();
    currentMonthMovements.forEach(movement => {
      if (movement.TIPO_COMPROBANTE === 'CB' || movement.TIPO_COMPROBANTE === 'RC') {
        const matchingFactura = currentMonthMovements.find(
          m => m.TIPO_COMPROBANTE === 'FB' &&
               m.PREFIJO === movement.PREFIJO &&
               m.NUMERO === movement.NUMERO &&
               !currentMonthMatchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`)
        );

        if (matchingFactura) {
          currentMonthMatchedInvoices.set(`${matchingFactura.PREFIJO}-${matchingFactura.NUMERO}`, true);
        }
      }
    });
    const currentMonthFb = currentMonthMovements
      .filter(m => m.TIPO_COMPROBANTE === 'FB' && !currentMonthMatchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`))
      .reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES), 0);
    const currentMonthCbRc = currentMonthMovements
      .filter(m => (m.TIPO_COMPROBANTE === 'CB' || m.TIPO_COMPROBANTE === 'RC'))
      .reduce((sum, m) => {
        const hasMatchingInvoice = currentMonthMovements.some(
          inv => inv.TIPO_COMPROBANTE === 'FB' &&
                inv.PREFIJO === m.PREFIJO &&
                inv.NUMERO === m.NUMERO
        );
        return hasMatchingInvoice ? sum : sum + parseFloat(m.TOTAL_EN_DOLARES);
      }, 0);
    currentMonthFbTotal += currentMonthFb;
    currentMonthCbRcTotal += currentMonthCbRc;
    return {
      companyCode: group.companyCode,
      companyName: group.companyName,
      fbTotal,
      cbRcTotal,
      balance: fbTotal - cbRcTotal,
      totalAmount,
      movements: group.movements
    };
  });

  return {
    processedGroups,
    currentMonthTotals: {
      fbTotal: currentMonthFbTotal,
      cbRcTotal: currentMonthCbRcTotal,
      balance: currentMonthFbTotal - currentMonthCbRcTotal
    }
  };
}



export const MOCK_ACCOUNT_DATA: AccountData[] = [
  {
    id: 1,
    descripcion: 'Saldo en Cuenta',
    vencido: '0.00',
    aVencer: '0.00',
    total: '0.00',
    credito: '0.00',
    creditoDisponible: '0.00',
    moneda: 'USD'
  }
];

export const MOCK_TRANSACTION_HISTORY: TransactionData[] = [];
export const MOCK_MOVEMENT_DATA: MovementData[] = [];
export const MOCK_PROCESSED_MOVEMENTS: ProcessedMovementGroup[] = processMovementData(MOCK_MOVEMENT_DATA);

export function useColaboradorData(_options?: UseApiOptions) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [rawMovements, setRawMovements] = useState<MovementData[]>([]);
  const [processedMovements, setProcessedMovements] = useState<ProcessedMovementGroup[]>([]);
  const [rawCashMovements, setRawCashMovements] = useState<MovementData[]>([]);
  const [processedCashMovements, setProcessedCashMovements] = useState<ProcessedMovementGroup[]>([]);
  const [currentMonthCashTotals, setCurrentMonthCashTotals] = useState<{
    fbTotal: number,
    cbRcTotal: number,
    balance: number
  }>({ fbTotal: 0, cbRcTotal: 0, balance: 0 });

  useEffect(() => {

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const codCtaCte = user?.id_ctacte?.toString() || '';

        if (!codCtaCte) {
          console.warn('User account data is missing. Using mock data instead.');
        }


        try {
          if (codCtaCte) {
            const movementResponse = await fetchMovementData(codCtaCte);
            if (movementResponse.success && movementResponse.data) {
              setRawMovements(movementResponse.data);
              setProcessedMovements(processMovementData(movementResponse.data));
            }

            // Fetch cash movements
            const cashMovementResponse = await fetchCashMovementData(codCtaCte);
            if (cashMovementResponse.success && cashMovementResponse.data) {
              setRawCashMovements(cashMovementResponse.data);
              const { processedGroups, currentMonthTotals } = processCashMovementData(cashMovementResponse.data);
              setProcessedCashMovements(processedGroups);
              setCurrentMonthCashTotals(currentMonthTotals);
            }
          }
        } catch (movementError) {
          console.error('Error fetching movement data, using mock data:', movementError);
          setRawMovements(MOCK_MOVEMENT_DATA);
          setProcessedMovements(MOCK_PROCESSED_MOVEMENTS);
          setRawCashMovements(MOCK_MOVEMENT_DATA);
          setProcessedCashMovements(processCashMovementData(MOCK_MOVEMENT_DATA).processedGroups);
          setCurrentMonthCashTotals({ fbTotal: 0, cbRcTotal: 0, balance: 0 });
        }
      } catch (err) {
        console.error('Error in useColaboradorData:', err);
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user]);

  return {
    rawMovements,
    processedMovements,
    rawCashMovements,
    processedCashMovements,
    currentMonthCashTotals,
    loading,
    error,
    refetch: async () => {
      setLoading(true);
      setError(null);

      try {
        const codCtaCte = user?.id_ctacte?.toString() || '';
        if (!codCtaCte) {
          console.warn('User account data is missing.');
        }
        try {
          if (codCtaCte) {
            const movementResponse = await fetchMovementData(codCtaCte);
            if (movementResponse.success && movementResponse.data) {
              setRawMovements(movementResponse.data);
              setProcessedMovements(processMovementData(movementResponse.data));
            }
            const cashMovementResponse = await fetchCashMovementData(codCtaCte);
            if (cashMovementResponse.success && cashMovementResponse.data) {
              setRawCashMovements(cashMovementResponse.data);
              const { processedGroups, currentMonthTotals } = processCashMovementData(cashMovementResponse.data);
              setProcessedCashMovements(processedGroups);
              setCurrentMonthCashTotals(currentMonthTotals);
            }
          }
        } catch (movementError) {
          console.error('Error fetching movement data, using mock data:', movementError);
          setRawMovements(MOCK_MOVEMENT_DATA);
          setProcessedMovements(MOCK_PROCESSED_MOVEMENTS);
          setRawCashMovements(MOCK_MOVEMENT_DATA);
          setProcessedCashMovements(processCashMovementData(MOCK_MOVEMENT_DATA).processedGroups);
          setCurrentMonthCashTotals({ fbTotal: 0, cbRcTotal: 0, balance: 0 });
        }
      } catch (err) {
        console.error('Error in useColaboradorData refetch:', err);
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      } finally {
        setLoading(false);
      }
    }
  };
}
