import BlankPageComponent from './BlankPage';
import withPageMetadata from '../../hoc/withPageMetadata';
import { registerPageModule } from '../../routes/PageMetadata';

const BlankPage = withPageMetadata(BlankPageComponent);

if (BlankPageComponent.pageMetadata) {
  try {
    registerPageModule('BlankPage', BlankPageComponent, {
      ...BlankPageComponent.pageMetadata,
      path: BlankPageComponent.pageMetadata.path || '/main/blank'
    });
  } catch (error) {
    console.error('[BLANKPAGE] Error registering BlankPage module:', error);
  }
}

export const pageMetadata = BlankPageComponent.pageMetadata;

export default BlankPage;
