.left-sidebar {
    box-shadow: none !important;
}

.offcanvas {
    --bs-offcanvas-bg: #152332 !important;
}

hr {
    border-top: 1px solid #ebf1f6;
    opacity: 0.1;
}

#main-wrapper {
    background-color: $body-bg-dark;
}

a {
    color: $text-dark-white !important;
}

.sidebar-nav #sidebarnav>.sidebar-item .first-level,
.sidebar-nav #sidebarnav>.sidebar-item .second-level {
    background-color: $bg-dark !important;
}

.left-sidebar {
    background-color: $bg-dark;
}

//Apps Menu
#mobilenavbar {
    .brand-logo {
        padding: 0 0;
    }

    .sidebar-nav #sidebarnav>.sidebar-item .first-level,
    .sidebar-nav #sidebarnav>.sidebar-item .second-level {
        background-color: transparent !important;
    }

    .sidebar-nav {
        ul {
            .sidebar-item {
                .sidebar-link {
                    color: $white !important;
                    font-weight: 600;
                    opacity: 0.8 !important;

                    &.active {
                        background-color: transparent !important;
                    }
                }
            }

            li {
                .has-arrow::after {
                    border-color: $white !important;
                }

                .has-arrow.active::after {
                    border-color: $white !important;
                }
            }
        }
    }
}

&[data-layout="horizontal"] {
    .sidebar-nav {
        #sidebarnav {
            flex-wrap: wrap;

            >.sidebar-item>.has-arrow:after {
                display: block;
            }

            .sidebar-item {
                .first-level {
                    .sidebar-link {
                        color: $white !important;
                    }
                }
            }
        }
    }
}

.bg-white, .preloader {
    background-color: $body-bg-dark !important;
}

.card {
    --bs-card-bg: #152332;
    --bs-card-title-color: #eaeff4;
    --bs-card-subtitle-color: #7c8fac;
    --bs-card-box-shadow: rgba(145, 158, 171, 0.3) 0px 0px 2px 0px,
        rgba(145, 158, 171, 0.02) 0px 12px 24px -4px;
}

.dropdown-menu {
    --bs-dropdown-box-shadow: rgba(145, 158, 171, 0.3) 0px 0px 2px 0px,
        rgba(145, 158, 171, 0.02) 0px 12px 24px -4px;
    --bs-dropdown-bg: #152332;
    --bs-dropdown-link-color: #EAEFF4;
    --bs-dropdown-link-hover-color: #EAEFF4;
    --bs-dropdown-link-hover-bg: #EDF5FD;
}

.text-dark {
    color: $heading-color !important;
}


.card,
.modal-content {
    background-color: $bg-dark !important;
}


.bg-light {
    background-color: $bg-dark !important;
    color: var(--bs-card-title-color) !important;
}

.text-bg-light,
.bg-light {
    background-color: #26313d !important;
    color: var(--bs-card-title-color) !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: var(--bs-primary-bg-subtle);
}

.form-select,
.form-select:focus,
.form-control,
.form-control:focus {
    border-color: $border-color-dark;
    color: $body-color-dark;
    background-color: $body-bg-dark;
}

.connect-sorting-todo,
.connect-sorting {
    background: var(--bs-dark-bg-subtle);
}

a:hover {

    h6,
    .h6,
    h5,
    .h5,
    h4,
    .h4,
    h3,
    .h3,
    h2,
    .h2,
    h1,
    .h1 {
        color: var(--bs-primary);
    }
}

tr[style*="cursor: pointer"]:hover {
    h6,
    .h6,
    h5,
    .h5,
    h4,
    .h4,
    h3,
    .h3,
    h2,
    .h2,
    h1,
    .h1 {
        color: var(--bs-heading-color) !important;
    }
}