import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { FA6 } from '@/config/icons/iconUtils';
import { useAuth, useLayout } from '@/context';
import { DdProfile } from './dropdowns';
import assetConfig, { getDefaultProfileImage } from '@/config/assetConfig.ts';
import useSidebar from '@/hooks/useSidebar';
import { ROUTES } from '@/routes/routeConstants';

interface HeaderProps {
  toggleSidebar?: () => void;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar: propToggleSidebar }) => {
  const {
    layout,
    breakpointValues
  } = useLayout();
  const { user } = useAuth();

  const { handleSidebarResponsive } = useSidebar();

  const mobileNavRef = useRef<HTMLDivElement>(null);
  const [navbarCollapsed, setNavbarCollapsed] = useState(true);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1200);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);

      if (window.innerWidth >= 992) {
        setNavbarCollapsed(true);

        if (mobileNavRef.current) {
          mobileNavRef.current.classList.remove('show', 'collapsing');
          mobileNavRef.current.classList.add('collapse');
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);



  const toggleNavbarCollapse = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setNavbarCollapsed(!navbarCollapsed);
  };

  const handleSidebarToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    if (propToggleSidebar) {
      propToggleSidebar();
    } else {
      handleSidebarResponsive();
    }
  };

  return (
    <>
      <header className="topbar rounded-0 border-0 bg-primary">
        <nav className="navbar navbar-expand-lg px-lg-0 px-3 py-0">
          <div className="d-none d-lg-block">
            <div className="brand-logo d-flex align-items-center justify-content-between">
              <Link to={ROUTES.DASHBOARD} className="text-nowrap logo-img d-flex align-items-center gap-2">
                <b className="logo-icon">
                  <img
                    src={assetConfig.importedAssets.logos.icon.light}
                    alt="homepage"
                    className="light-logo"
                  />
                </b>
                {/* Logo text - DISABLED */}
                {/* {layoutSettings.sidebarType !== 'mini-sidebar' && (
                  <span className="logo-text">
                    <img
                      src={getLogoPath('text')}
                      alt="homepage"
                      className="light-logo ps-2"
                    />
                  </span>
                )} */}
              </Link>
            </div>
          </div>

          <ul className="navbar-nav gap-2">
            {(!layout.isHorizontal || windowWidth < breakpointValues.xl) && (
              <li className="nav-item nav-icon-hover-bg rounded-circle">
                <a
                  className="nav-link nav-icon-hover sidebartoggler"
                  id="headerCollapse"
                  href="#"
                  onClick={handleSidebarToggle}
                >
                  <FA6.FaBars className="fs-6" />
                </a>
              </li>
            )}
          </ul>

          <div className="d-block d-lg-none">
            <Link to={ROUTES.DASHBOARD} className="text-nowrap logo-img d-flex align-items-center gap-2">
              <b className="logo-icon">
                <img
                  src={assetConfig.importedAssets.logos.icon.light}
                  alt="homepage"
                  width="35"
                  className="light-logo"
                />
              </b>
            </Link>
          </div>

          <ul className="navbar-nav flex-row gap-2 align-items-center justify-content-center d-flex d-lg-none">
            <li className="nav-item dropdown nav-icon-hover-bg rounded-circle">
              <a
                className="navbar-toggler nav-link text-white nav-icon-hover border-0"
                href="#"
                onClick={toggleNavbarCollapse}
                aria-expanded={!navbarCollapsed}
                aria-label="Toggle navigation"
              >
                <span>
                  <FA6.FaEllipsis className="fs-7" />
                </span>
              </a>
            </li>
          </ul>

          <div className={`collapse navbar-collapse justify-content-end ${!navbarCollapsed ? 'show' : ''}`} id="navbarNav" ref={mobileNavRef}>
            <div className="d-flex align-items-center justify-content-between py-2 py-lg-0">
              <ul className="navbar-nav flex-row align-items-center justify-content-center d-flex d-lg-none">
              </ul>
              <ul className="navbar-nav gap-2 flex-row ms-auto align-items-center justify-content-center">
                <li className="nav-item dropdown">
                  <a
                    className="nav-link nav-icon-hover"
                    id="profileDropdown"
                    role="button"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    <img
                      src={user?.avatar || getDefaultProfileImage()}
                      alt={`${user?.datosBLaboro?.Nombre || 'Usuario'} avatar`}
                      className="rounded-circle"
                      width="30"
                      height="30"
                    />
                  </a>
                  <DdProfile />
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </header>
    </>
  );
};

export default Header;
