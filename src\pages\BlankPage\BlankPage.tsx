import React from 'react';
import { FA6 } from '@/config/icons/iconUtils';
import { PageMetadata } from '@/hoc/withPageMetadata';
import { PERMISSIONS } from '@/config/authConfig';
type BlankPageComponentType = React.FC & { pageMetadata: PageMetadata };

const BlankPageComponent: BlankPageComponentType = () => {

  return (
    <div className="container-fluid">
      <div className="card">
        <div className="card-body">
          <h5 className="card-title fw-semibold mb-4">Pagina en blanco</h5>
          <p className="mb-0">Esto es una pagina en blanco.</p>
        </div>
      </div>
    </div>
  );
};

BlankPageComponent.pageMetadata = {
  title: 'Pagina en blanco',
  description: 'Pagina en blanco para nuevo contenido',
  icon: <FA6.FaFile className="fs-6" />,
  showInMenu: false,
  menuOrder: 10,
  path: '/main/blank',
  requiresAuth: true,
  section: 'HOME',
  permissions: {
    requiredPermission: PERMISSIONS.NONE,
    resourceId: 'blank'
  }
};

export const pageMetadata = BlankPageComponent.pageMetadata;
export default BlankPageComponent;
