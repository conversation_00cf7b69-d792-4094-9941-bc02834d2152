export interface ApiResponse<T> {
  data: T;
  status?: number;
  message?: string;
  error?: string;
}
import { apiUtils, apiEndpoints } from '../config/apiConfig';
const BASE_URL = apiEndpoints.api;

export async function get<T>(endpoint: string, params?: Record<string, unknown>): Promise<ApiResponse<T>> {
  try {
    const queryString = params ? apiUtils.formatQueryParams(params) : '';
    const response = await fetch(`${BASE_URL}${endpoint}${queryString}`);
    const data = await response.json();

    return {
      data,
      status: response.status,
      message: response.ok ? 'Success' : 'Error'
    };
  } catch (error) {
    return {
      data: null as T,
      status: 500,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export async function post<T>(endpoint: string, body: unknown): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });
    const data = await response.json();

    return {
      data,
      status: response.status,
      message: response.ok ? 'Success' : 'Error'
    };
  } catch (error) {
    return {
      data: null as T,
      status: 500,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}