
export function formatDate(dateString: string, format: string = 'YYYY-MM-DD'): string {
  let date: Date;

  if (dateString.includes('-')) {
    const [year, month, day] = dateString.split('-').map((num: string) => parseInt(num, 10));
    date = new Date(year, month - 1, day);
  } else {
    date = new Date(dateString);
  }

  if (isNaN(date.getTime())) {
    return dateString;
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  let formattedDate = format;
  formattedDate = formattedDate.replace('YYYY', year.toString());
  formattedDate = formattedDate.replace('MM', month);
  formattedDate = formattedDate.replace('DD', day);
  formattedDate = formattedDate.replace('HH', hours);
  formattedDate = formattedDate.replace('mm', minutes);
  formattedDate = formattedDate.replace('ss', seconds);

  return formattedDate;
}

export function formatCurrency(
  value: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(value);
}

export function formatNumber(value: number, locale: string = 'en-US'): string {
  return new Intl.NumberFormat(locale).format(value);
}

export function truncateString(str: string, length: number = 50, suffix: string = '...'): string {
  if (!str) return '';
  if (str.length <= length) {
    return str;
  }

  return str.substring(0, length - suffix.length) + suffix;
}

export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

export function formatPhoneNumber(phone: string, format: string = '(XXX) XXX-XXXX'): string {
  const cleaned = ('' + phone).replace(/\D/g, '');
  if (cleaned.length < 10) return phone;
  let result = format;
  let charIndex = 0;

  for (let i = 0; i < result.length; i++) {
    if (result[i] === 'X') {
      if (charIndex < cleaned.length) {
        result = result.substring(0, i) + cleaned[charIndex++] + result.substring(i + 1);
      }
    }
  }

  return result;
}