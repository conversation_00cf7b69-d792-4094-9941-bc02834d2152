
import React, { forwardRef } from 'react';
import { Form, InputGroup } from 'react-bootstrap';

export interface FormInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'size'> {
  id?: string;
  name: string;
  label?: string;
  helpText?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  size?: 'sm' | 'lg';
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  startText?: string;
  endText?: string;
  showCharCount?: boolean;
  maxLength?: number;
  value?: string | number | string[];
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
}

const FormInput = forwardRef<HTMLInputElement, FormInputProps>(
  (
    {
      id,
      name,
      label,
      helpText,
      error,
      required = false,
      disabled = false,
      readOnly = false,
      size,
      startIcon,
      endIcon,
      startText,
      endText,
      showCharCount = false,
      maxLength,
      type = 'text',
      value = '',
      placeholder,
      onChange,
      onFocus,
      onBlur,
      ...rest
    },
    ref
  ) => {
    const inputId = id || `form-input-${name}`;
    const hasStartAddon = !!startIcon || !!startText;
    const hasEndAddon = !!endIcon || !!endText;
    const hasAddons = hasStartAddon || hasEndAddon;
    const currentLength = typeof value === 'string' ? value.length : 0;
    const renderInput = () => {
      const inputProps = {
        id: inputId,
        name,
        type,
        placeholder,
        disabled,
        readOnly,
        required,
        value,
        onChange,
        onFocus,
        onBlur,
        maxLength,
        className: `form-control${error ? ' is-invalid' : ''}${size ? ` form-control-${size}` : ''}`,
        ...rest
      };

      if (hasAddons) {
        return (
          <InputGroup size={size}>
            {startIcon && <InputGroup.Text>{startIcon}</InputGroup.Text>}
            {startText && <InputGroup.Text>{startText}</InputGroup.Text>}
            <Form.Control ref={ref} {...inputProps} />
            {endIcon && <InputGroup.Text>{endIcon}</InputGroup.Text>}
            {endText && <InputGroup.Text>{endText}</InputGroup.Text>}
          </InputGroup>
        );
      }

      return <Form.Control ref={ref} {...inputProps} />;
    };

    return (
      <div className="mb-3">
        {label && (
          <label
            htmlFor={inputId}
            className={`form-label${required ? ' required' : ''}`}
          >
            {label}
          </label>
        )}

        {renderInput()}

        {showCharCount && maxLength && (
          <div className="d-flex justify-content-end mt-1">
            <small className={`text-muted${currentLength > maxLength ? ' text-danger' : ''}`}>
              {currentLength}/{maxLength}
            </small>
          </div>
        )}

        {error && (
          <div className="invalid-feedback d-block">
            {error}
          </div>
        )}

        {helpText && (
          <div className="form-text text-muted">
            {helpText}
          </div>
        )}
      </div>
    );
  }
);

FormInput.displayName = 'FormInput';

export default FormInput;
