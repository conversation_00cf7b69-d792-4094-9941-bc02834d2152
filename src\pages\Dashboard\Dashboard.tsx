import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { FA6 } from '@/config';
import { PageMetadata } from '@/hoc/withPageMetadata';
import { PERMISSIONS } from '@/config/authConfig';

const Dashboard: React.FC & { pageMetadata: PageMetadata } = () => {
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());

  const currentDate = new Date();

  let greeting = "Bienvenido!";


  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('es-PY', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  return (
    <>
      <style>{`
        .dashboard-container {
          min-height: 100vh;
          padding: 2rem 0;
        }
        .dashboard-content {
          position: relative;
        }

        .welcome-header {
          color: #1f2937;
          font-weight: 600;
          font-size: 2.5rem;
          margin-bottom: 0.5rem;
        }
        .welcome-subtitle {
          color: #6b7280;
          font-size: 1rem;
          margin-bottom: 2rem;
        }
        .welcome-section {
          margin-bottom: 3rem;
          padding: 2rem 0;
        }

        .datetime-hero {
          background: #ffffff;
          border: 1px solid #e5e7eb;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }
        .time-display {
          font-weight: 700;
          color: #1f2937;
        }
        .date-badge {
          background: #f3f4f6;
          border-radius: 8px;
        }
        .clock-icon-container {
          background: #f8f9fa;
          border: 1px solid #e5e7eb;
        }


        @media (max-width: 768px) {
          .welcome-header {
            font-size: 2rem !important;
          }
          .time-display {
            font-size: 1.5rem !important;
          }
          .clock-icon-container {
            width: 40px !important;
            height: 40px !important;
          }
        }
      `}</style>
      <div className="dashboard-container">
        <Container fluid className="py-1 dashboard-content">
          <div className="text-center welcome-section">
            <h1 className="welcome-header">{greeting}</h1>

            <div className="row justify-content-center">
              <div className="col-lg-4 col-md-4">
                <div className="datetime-hero rounded-3 p-2">
                  <div className="d-flex align-items-center justify-content-center gap-2">
                    <div className="clock-icon-container rounded-circle d-flex align-items-center justify-content-center" style={{width: '50px', height: '50px'}}>
                      <FA6.FaClock className="text-primary" size={20} />
                    </div>
                    <div className="text-center">
                      <div className="time-display" style={{fontSize: '1.8rem'}}>
                        {formatTime(currentTime)}
                      </div>
                      <div className="date-badge rounded-pill px-3 py-1 mt-1">
                        <small className="text-muted fw-medium">
                          {new Intl.DateTimeFormat('es-PY', {
                            weekday: 'long',
                            day: 'numeric',
                            month: 'long',
                            year:'numeric'
                          }).format(currentDate)}
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Row className="justify-content-center g-4 mb-5">
            <Col lg={4} md={6} sm={6} xs={12}>
              <Card className="border-0 shadow-sm">
                <Card.Body className="p-3">
                  <div
                    className="d-flex align-items-center justify-content-between py-4 px-5 bg-light rounded-3"
                    onClick={() => navigate('/main/miperfil')}
                    style={{
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      userSelect: 'none'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#e3f2fd';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = '#f8f9fa';
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                    onMouseDown={(e) => {
                      e.currentTarget.style.transform = 'translateY(0) scale(0.98)';
                    }}
                    onMouseUp={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px) scale(1)';
                    }}
                  >
                    <div className="d-flex align-items-center">
                      <FA6.FaUserTie className="text-primary me-3" size={24} />
                      <div>
                        <div className="fw-bold text-dark">Mi Perfil</div>
                        <div className="text-muted small">Información personal</div>
                      </div>
                    </div>
                    <FA6.FaChevronRight className="text-muted" size={16} />
                  </div>
                </Card.Body>
              </Card>
            </Col>

            <Col lg={4} md={6} sm={6} xs={12}>
              <Card className="border-0 shadow-sm">
                <Card.Body className="p-3">
                  <div
                    className="d-flex align-items-center justify-content-between py-4 px-4 bg-light rounded-3"
                    onClick={() => navigate('/main/estadodecuenta')}
                    style={{
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      userSelect: 'none'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = '#e3f2fd';
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = '#f8f9fa';
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                    onMouseDown={(e) => {
                      e.currentTarget.style.transform = 'translateY(0) scale(0.98)';
                    }}
                    onMouseUp={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px) scale(1)';
                    }}
                  >
                    <div className="d-flex align-items-center">
                      <FA6.FaMoneyBillTransfer className="text-primary me-3" size={24} />
                      <div>
                        <div className="fw-bold text-dark">Estado de Cuenta</div>
                        <div className="text-muted small">Ver tus movimientos y saldo</div>
                      </div>
                    </div>
                    <FA6.FaChevronRight className="text-muted" size={16} />
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </div>
    </>
  );
};

// Define page metadata directly in the component
Dashboard.pageMetadata = {
  title: 'Inicio',
  description: 'Página de bienvenida',
  icon: <FA6.FaHouse className="fs-6" />,
  showInMenu: true,
  menuOrder: 1,
  path: '/main/dashboard', // Use the MAIN_ROUTE_PREFIX
  requiresAuth: true,
  section: 'HOME', // Explicitly set the section
  permissions: {
    requiredPermission: PERMISSIONS.NONE,
    resourceId: 'dashboard'
  }
};

export default Dashboard;
