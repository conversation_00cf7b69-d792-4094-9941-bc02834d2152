import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import { PageContext } from '../../context/PageContext';

interface BreadcrumbProps {
  title?: string;
  subtitle?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ 
  title: defaultTitle = 'Página', 
  subtitle: defaultSubtitle = 'Inicio' 
}) => {
  const { pageMeta } = useContext(PageContext);
  const title = pageMeta?.title || defaultTitle;
  return (
    <div className="mb-2">
      <h4 className="mb-0">{title}</h4>
      <div className="d-flex align-items-center">
        <Link to="/dashboard" className="text-muted text-decoration-none">{defaultSubtitle}</Link>
        <span className="mx-1"> &gt; </span>
        <span className="text-muted">{title}</span>
      </div>
    </div>
  );
};

export default Breadcrumb; 