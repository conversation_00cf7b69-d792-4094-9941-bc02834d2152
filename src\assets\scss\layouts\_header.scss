.topbar {
  position: fixed;
  padding: 0 18px;
  top: 0;
  width: 100%;
  background: var(--bs-card-bg);
  z-index: 50;
  box-shadow: 1px 0 7px rgba(0, 0, 0, 0.05);

  @media screen and (max-width:768px){
    padding: 0 0px;
  }

  .navbar {
    min-height: $headerHeight;
    max-height: $headerHeight;

    .navbar-toggler:focus {
      box-shadow: none;
    }

    .navbar-nav {
      .hover-dd {
        position: relative;

       

        &:hover {
          .dropdown-menu {
            display: block;

            li {
              line-height: normal;
            }

          }
        }
      }

      .mega-dropdown {
        position: static;

        .dropdown-menu {
          padding: 30px;
          width: 100%;
          max-height: 480px;
          overflow: auto;
          z-index: 2;
        }
      }

      .dropdown-menu {
        position: absolute;
        min-width: 200px;
        top: 100%;
        @include media-breakpoint-up(md) {
          &.content-dd {
            width: $dropdown-width;
          }
        }
      }

      .nav-item {
        &.dropdown {
          .dropdown-menu-end {
            right: 0;
            left: auto;
            top: 100%;
          }
          &:hover {
            .dropdown-menu {
              display: block;
            }
          }
        }
        .nav-link {
          display: flex;
          height: 40px;
          width: 40px;
          justify-content: center;
          font-size: 20px;
          display: flex;
          color: $white;
          align-items: center;
        }
      }
    }
  }
}

.dropdown-menu-nav {
  min-width: 860px !important;
  width: 100%;
}


.profile-status {
  border: 2px solid #fff;
  height: 10px;
  left: 30px;
  top: -2px;
  width: 10px;
}

.message-body {
  max-height: 360px;
}

/*******************
Notify
*******************/

.notify {
  position: relative;
  top: -20px;
  right: -8px;

  .heartbit {
    position: absolute;
    top: -5px;
    right: -2px;
    height: 18px;
    width: 18px;
    z-index: 10;
    border: 2px solid $danger;
    border-radius: 70px;
    animation: heartbit 1s ease-out;
    -moz-animation: heartbit 1s ease-out;
    -moz-animation-iteration-count: infinite;
    -o-animation: heartbit 1s ease-out;
    -o-animation-iteration-count: infinite;
    -webkit-animation: heartbit 1s ease-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
  }

  .point {
    width: 4px;
    height: 4px;
    border-radius: 30px;
    position: absolute;
    right: 5px;
    top: 2px;
    background-color: $danger;
    position: absolute;
  }
}

@keyframes heartbit {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  25% {
    transform: scale(0.1);
    opacity: 0.1;
  }

  50% {
    transform: scale(0.5);
    opacity: 0.3;
  }

  75% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@include media-breakpoint-down(md) {
  .topbar {
    .navbar-nav {
      .dropdown-menu {
        position: absolute;
        width: 100%;
      }

      .nav-item.dropdown {
        position: static;
      }
    }
  }
}

.dropdown-menu-end{
  right:15px;
  left: auto;
}

@media screen and (max-width: 767px){
  .topbar{
    .navbar{
      .navbar-nav{
        .dropdown-menu {
          right: 0;
        }
      }
    }
  }  
}
