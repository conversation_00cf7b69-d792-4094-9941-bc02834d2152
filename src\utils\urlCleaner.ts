import { ROUTES, MAIN_ROUTE_PREFIX, AUTH_ROUTE_PREFIX } from '@/routes/routeConstants';

export function isApiEndpoint(url: string): boolean {
  if (!url || typeof url !== 'string') return false;
  const cleanUrl = url.toLowerCase();
  return cleanUrl.includes('/api/') || cleanUrl.includes('/ws/');
}

export function shouldProcessUrl(url: string): boolean {
  if (!url || typeof url !== 'string') return false;
  if (isApiEndpoint(url)) return false;
  return true;
}

export function getSecureRedirect(isAuthenticated: boolean): string {
  return isAuthenticated ? ROUTES.DASHBOARD : ROUTES.LOGIN;
}

function isValidRoutePath(path: string): boolean {
  if (!path || typeof path !== 'string') return false;

  const normalizedPath = path.toLowerCase();
  return normalizedPath.startsWith('/main/') || normalizedPath.startsWith('/auth/');
}

export function processNavigationUrl(fullUrl: string, currentPath: string, isAuthenticated: boolean): {
  shouldRedirect: boolean;
  redirectTo?: string;
  reason?: string;
} {
  if (!shouldProcessUrl(fullUrl)) {
    return { shouldRedirect: false };
  }

  if (!currentPath || typeof currentPath !== 'string') {
    return {
      shouldRedirect: true,
      redirectTo: getSecureRedirect(isAuthenticated),
      reason: 'invalid_path'
    };
  }

  if (!isValidRoutePath(currentPath)) {
    return {
      shouldRedirect: true,
      redirectTo: getSecureRedirect(isAuthenticated),
      reason: 'invalid_route_structure'
    };
  }

  if (isAuthenticated) {
    if (!currentPath.startsWith(MAIN_ROUTE_PREFIX)) {
      return {
        shouldRedirect: true,
        redirectTo: ROUTES.DASHBOARD,
        reason: 'auth_redirect_to_main'
      };
    }
  } else {
    if (!currentPath.startsWith(AUTH_ROUTE_PREFIX)) {
      return {
        shouldRedirect: true,
        redirectTo: ROUTES.LOGIN,
        reason: 'unauth_redirect_to_login'
      };
    }
  }

  return { shouldRedirect: false };
}
