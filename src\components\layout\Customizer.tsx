import React, { useState } from 'react';
import { Button, Offcanvas } from 'react-bootstrap';
import { useTheme } from '../../context/ThemeContext';
import { useLayout } from '../../context/LayoutContext';
import { FA6 } from '../../config/icons/iconUtils';
import { ColorTheme, themeColors } from '../../config/themeConfig';

const Customizer: React.FC = () => {
  const { settings: themeSettings, updateSettings: updateThemeSettings } = useTheme();
  const { settings: layoutSettings, updateSettings: updateLayoutSettings } = useLayout();
  const [show, setShow] = useState(false);

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);

  const colorThemes: ColorTheme[] = Object.keys(themeColors) as ColorTheme[];

  return (
    <>
      <Button
        variant="primary"
        className="p-3 rounded-circle d-flex align-items-center justify-content-center customizer-btn"
        onClick={handleShow}
      >
        <FA6.FaGear className="fs-7 text-white" />
      </Button>

      <Offcanvas show={show} onHide={handleClose} placement="end" className="customizer">
        <Offcanvas.Header closeButton>
          <Offcanvas.Title className="fw-semibold">Settings</Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body className="h-n80" data-simplebar>
          {/* Theme Selection */}
          <h6 className="fw-semibold fs-4 mb-2">Theme</h6>
          <div className="d-flex flex-row gap-3 customizer-box" role="group">
            <input
              type="radio"
              className="btn-check"
              name="theme-layout"
              id="light-layout"
              checked={themeSettings.theme === 'light'}
              onChange={() => updateThemeSettings({ theme: 'light' })}
              autoComplete="off"
            />
            <label className="btn p-9 btn-outline-primary rounded" htmlFor="light-layout">
              <FA6.FaSun className="icon fs-7 me-2" />
              Light
            </label>
            <input
              type="radio"
              className="btn-check"
              name="theme-layout"
              id="dark-layout"
              checked={themeSettings.theme === 'dark'}
              onChange={() => updateThemeSettings({ theme: 'dark' })}
              autoComplete="off"
            />
            <label className="btn p-9 btn-outline-primary rounded" htmlFor="dark-layout">
              <FA6.FaMoon className="icon fs-7 me-2" />
              Dark
            </label>
          </div>

          {/* Theme Direction */}
          <h6 className="mt-5 fw-semibold fs-4 mb-2">Theme Direction</h6>
          <div className="d-flex flex-row gap-3 customizer-box" role="group">
            <input
              type="radio"
              className="btn-check"
              name="direction-l"
              id="ltr-layout"
              checked={layoutSettings.direction === 'ltr'}
              onChange={() => updateLayoutSettings({ direction: 'ltr' })}
              autoComplete="off"
            />
            <label className="btn p-9 btn-outline-primary rounded" htmlFor="ltr-layout">
              <FA6.FaAlignLeft className="icon fs-7 me-2" />
              LTR
            </label>
            <input
              type="radio"
              className="btn-check"
              name="direction-l"
              id="rtl-layout"
              checked={layoutSettings.direction === 'rtl'}
              onChange={() => updateLayoutSettings({ direction: 'rtl' })}
              autoComplete="off"
            />
            <label className="btn p-9 btn-outline-primary rounded" htmlFor="rtl-layout">
              <FA6.FaAlignRight className="icon fs-7 me-2" />
              RTL
            </label>
          </div>

          {/* Theme Colors */}
          <h6 className="mt-5 fw-semibold fs-4 mb-2">Theme Colors</h6>
          <div className="d-flex flex-row flex-wrap gap-3 customizer-box color-pallete" role="group">
            {colorThemes.map((theme) => (
              <React.Fragment key={theme}>
                <input
                  type="radio"
                  className="btn-check"
                  name="color-theme-layout"
                  id={theme}
                  autoComplete="off"
                  checked={themeSettings.colorTheme === theme}
                  onChange={() => updateThemeSettings({ colorTheme: theme })}
                />
                <label
                  className="btn p-9 btn-outline-primary d-flex align-items-center justify-content-center rounded"
                  htmlFor={theme}
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  data-bs-title={theme.toUpperCase()}
                >
                  <div
                    className="theme-color-preview rounded-circle d-flex align-items-center justify-content-center"
                    style={{
                      backgroundColor: themeColors[theme].primary,
                      width: '30px',
                      height: '30px',
                      border: themeSettings.colorTheme === theme ? '2px solid #000' : '1px solid #ddd'
                    }}
                  >
                    {themeSettings.colorTheme === theme && (
                      <FA6.FaCheck className="text-white fs-6" />
                    )}
                  </div>
                </label>
              </React.Fragment>
            ))}
          </div>

          {/* Layout Type */}
          <h6 className="mt-5 fw-semibold fs-4 mb-2">Layout Type</h6>
          <div className="d-flex flex-row gap-3 customizer-box" role="group">
            <div>
              <input
                type="radio"
                className="btn-check"
                name="page-layout"
                id="vertical-layout"
                checked={layoutSettings.layout === 'vertical'}
                onChange={() => updateLayoutSettings({ layout: 'vertical' })}
                autoComplete="off"
              />
              <label className="btn p-9 btn-outline-primary rounded" htmlFor="vertical-layout">
                <FA6.FaArrowsUpDown className="icon fs-7 me-2" />
                Vertical
              </label>
            </div>
            <div>
              <input
                type="radio"
                className="btn-check"
                name="page-layout"
                id="horizontal-layout"
                checked={layoutSettings.layout === 'horizontal'}
                onChange={() => updateLayoutSettings({ layout: 'horizontal' })}
                autoComplete="off"
              />
              <label className="btn p-9 btn-outline-primary rounded" htmlFor="horizontal-layout">
                <FA6.FaArrowsLeftRight className="icon fs-7 me-2" />
                Horizontal
              </label>
            </div>
          </div>

          {/* Container Option */}
          <h6 className="mt-5 fw-semibold fs-4 mb-2">Container Option</h6>
          <div className="d-flex flex-row gap-3 customizer-box" role="group">
            <input
              type="radio"
              className="btn-check"
              name="layout"
              id="boxed-layout"
              checked={layoutSettings.boxedLayout}
              onChange={() => updateLayoutSettings({ boxedLayout: true })}
              autoComplete="off"
            />
            <label className="btn p-9 btn-outline-primary rounded" htmlFor="boxed-layout">
              <FA6.FaBox className="icon fs-7 me-2" />
              Boxed
            </label>
            <input
              type="radio"
              className="btn-check"
              name="layout"
              id="full-layout"
              checked={!layoutSettings.boxedLayout}
              onChange={() => updateLayoutSettings({ boxedLayout: false })}
              autoComplete="off"
            />
            <label className="btn p-9 btn-outline-primary rounded" htmlFor="full-layout">
              <FA6.FaExpand className="icon fs-7 me-2" />
              Full
            </label>
          </div>

          {/* Sidebar Type */}
          {layoutSettings.layout === 'vertical' && (
            <>
              <h6 className="fw-semibold fs-4 mb-2 mt-5">Sidebar Type</h6>
              <div className="d-flex flex-row gap-3 customizer-box" role="group">
                <div>
                  <input
                    type="radio"
                    className="btn-check"
                    name="sidebar-type"
                    id="full-sidebar"
                    checked={layoutSettings.sidebarType === 'full'}
                    onChange={() => updateLayoutSettings({ sidebarType: 'full' })}
                    autoComplete="off"
                  />
                  <label className="btn p-9 btn-outline-primary rounded" htmlFor="full-sidebar">
                    <FA6.FaBars className="icon fs-7 me-2" />
                    Full
                  </label>
                </div>
                <div>
                  <input
                    type="radio"
                    className="btn-check"
                    name="sidebar-type"
                    id="mini-sidebar"
                    checked={layoutSettings.sidebarType === 'mini-sidebar'}
                    onChange={() => updateLayoutSettings({ sidebarType: 'mini-sidebar' })}
                    autoComplete="off"
                  />
                  <label className="btn p-9 btn-outline-primary rounded" htmlFor="mini-sidebar">
                    <FA6.FaBarsStaggered className="icon fs-7 me-2" />
                    Collapse
                  </label>
                </div>
              </div>
            </>
          )}

          {/* Card With */}
          <h6 className="mt-5 fw-semibold fs-4 mb-2">Card With</h6>
          <div className="d-flex flex-row gap-3 customizer-box" role="group">
            <input
              type="radio"
              className="btn-check"
              name="card-layout"
              id="card-with-border"
              checked={themeSettings.cardBorder}
              onChange={() => updateThemeSettings({ cardBorder: true })}
              autoComplete="off"
            />
            <label className="btn p-9 btn-outline-primary rounded" htmlFor="card-with-border">
              <FA6.FaBorderAll className="icon fs-7 me-2" />
              Border
            </label>
            <input
              type="radio"
              className="btn-check"
              name="card-layout"
              id="card-without-border"
              checked={!themeSettings.cardBorder}
              onChange={() => updateThemeSettings({ cardBorder: false })}
              autoComplete="off"
            />
            <label className="btn p-9 btn-outline-primary rounded" htmlFor="card-without-border">
              <FA6.FaSquare className="icon fs-7 me-2" />
              Shadow
            </label>
          </div>
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
};

export default Customizer;
