import React, { createContext, useState, useContext, ReactNode, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { PageContextType, routes, RouteDefinition } from '../routes/PageMetadata';
import { AUTH_CONFIG } from '../config/authConfig';

export interface PageMeta {
  title?: string;

  description?: string;

  icon?: React.ReactNode;

  breadcrumbs?: Array<{ label: string; path?: string }>;
}

const defaultPageContext: PageContextType = {
  pageMeta: {},
  setPageMeta: () => {
    console.warn('setPageMeta llamado antes de que el contexto fuera inicializado');
  }
};

export const PageContext = createContext<PageContextType>(defaultPageContext);

const findRouteByPath = (path: string): RouteDefinition | undefined => {
  if (path === '/' || path === '') {
    return routes.find(route => route.path === AUTH_CONFIG.homePath);
  }

  let normalizedPath = path;
  if (!normalizedPath.startsWith('/')) {
    normalizedPath = `/${normalizedPath}`;
  }

  let route = routes.find(route => route.path === normalizedPath);

  if (!route) {
    route = routes.find(route => route.path.substring(1) === normalizedPath.substring(1));
  }

  return route;
};

const generateBreadcrumbs = (route: RouteDefinition): Array<{ label: string; path?: string }> => {
  const breadcrumbs: Array<{ label: string; path?: string }> = [];

  breadcrumbs.push({ label: 'Home', path: AUTH_CONFIG.homePath });

  if (route.meta?.title) {
    breadcrumbs.push({ label: route.meta.title });
  }

  return breadcrumbs;
};

interface PageProviderProps {
  children: ReactNode;

  onMetaChange?: (meta: PageMeta) => void;
}

export const PageProvider: React.FC<PageProviderProps> = ({
  children,
  onMetaChange
}) => {
  const [pageMeta, setPageMetaState] = useState<PageMeta>({});

  const location = useLocation();
  const currentPathRef = useRef(location.pathname);
  const isFirstRender = useRef(true);

  useEffect(() => {
    const shouldUpdate = isFirstRender.current || currentPathRef.current !== location.pathname;

    if (isFirstRender.current) {
      isFirstRender.current = false;
    }

    if (!shouldUpdate) {
      return;
    }

    currentPathRef.current = location.pathname;

    const currentRoute = findRouteByPath(location.pathname);

    if (currentRoute && currentRoute.meta) {
      const routeMeta: PageMeta = {
        title: currentRoute.meta.title,
        description: currentRoute.meta.description,
        icon: currentRoute.meta.icon,
        breadcrumbs: generateBreadcrumbs(currentRoute)
      };

      setPageMetaState(routeMeta);
      if (onMetaChange) {
        onMetaChange(routeMeta);
      }
    }
  }, [location.pathname, onMetaChange]);

  const setPageMeta = (meta: PageMeta) => {
    setPageMetaState(prevMeta => {
      const newMeta = { ...prevMeta, ...meta };

      if (onMetaChange) {
        onMetaChange(newMeta);
      }

      return newMeta;
    });
  };

  return (
    <PageContext.Provider value={{ pageMeta, setPageMeta }}>
      {children}
    </PageContext.Provider>
  );
};


export const usePageMeta = () => {
  const context = useContext(PageContext);

  if (!context) {
    throw new Error('usePageMeta debe usarse dentro de un PageProvider');
  }

  return context;
};

export const withPageMeta = (
  Component: React.ComponentType<any>,
  defaultMeta: PageMeta
) => {
  const WithPageMeta = (props: any) => {
    const { setPageMeta } = usePageMeta();

    React.useEffect(() => {
      setPageMeta(defaultMeta);
    }, []);

    return <Component {...props} setPageMeta={setPageMeta} />;
  };

  return WithPageMeta;
};
