{"name": "weberp", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview --host"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroicons/react": "^2.2.0", "@types/classnames": "^2.3.4", "bootstrap": "^5.3.6", "classnames": "^2.5.1", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-datatable": "^0.0.31", "react-dom": "^19.1.0", "react-helmet": "^6.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.1", "remixicon": "^4.6.0", "simplebar": "^6.3.1", "simplebar-react": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/node-sass": "^4.11.8", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-helmet": "^6.1.11", "@vitejs/plugin-react": "^4.5.0", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "sass": "^1.89.0", "typescript": "~5.8.3", "typescript-eslint": "^8.33.0", "vite": "^6.3.5"}}