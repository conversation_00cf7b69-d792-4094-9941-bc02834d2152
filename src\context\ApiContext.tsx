
import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import {
  apiEndpoints,
  API_ROUTES,
  DEFAULT_ERROR_MESSAGES,
  apiUtils
} from '../config/apiConfig';

export type HttpMethod = 'GET' | 'POST';

export interface RequestOptions {
  method?: HttpMethod;
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, any>;
  cache?: boolean;
  timeout?: number;
}

export interface RequestState<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
  status: number | null;
}

export interface ApiContextType {
  isLoading: boolean;
  globalError: string | null;
  baseUrl: string;
  endpoints: typeof API_ROUTES;

  request: <T = any>(url: string, options?: RequestOptions) => Promise<T>;
  get: <T = any>(url: string, params?: Record<string, any>, options?: Omit<RequestOptions, 'method' | 'params'>) => Promise<T>;
  post: <T = any>(url: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>) => Promise<T>;
  buildUrl: (endpoint: string, params?: Record<string, any>) => string;
  clearCache: () => void;
  setGlobalError: (error: string | null) => void;
}

const defaultApiContext: ApiContextType = {
  isLoading: false,
  globalError: null,
  baseUrl: apiEndpoints.api,
  endpoints: API_ROUTES,
  request: async () => { throw new Error('API context not initialized'); },
  get: async () => { throw new Error('API context not initialized'); },
  post: async () => { throw new Error('API context not initialized'); },
  buildUrl: () => '',
  clearCache: () => {},
  setGlobalError: () => {}
};

export const ApiContext = createContext<ApiContextType>(defaultApiContext);
export const useApi = () => useContext(ApiContext);

interface ApiProviderProps {
  children: ReactNode;
  baseUrl?: string;
  defaultHeaders?: Record<string, string>;
}

export const ApiProvider: React.FC<ApiProviderProps> = ({
  children,
  baseUrl = apiEndpoints.api,
  defaultHeaders = { 'Content-Type': 'application/json', 'Accept': 'application/json' }
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [globalError, setGlobalError] = useState<string | null>(null);
  const [cache] = useState<Record<string, any>>({});


  const incrementActiveRequests = useCallback(() => {
    setIsLoading(true);
  }, []);

  const decrementActiveRequests = useCallback(() => {
    setIsLoading(false);
  }, []);

  const buildUrl = useCallback((endpoint: string, params?: Record<string, any>): string => {
    return apiUtils.buildUrl(endpoint, params);
  }, []);

  // Función para limpiar la caché
  const clearCache = useCallback(() => {
    Object.keys(cache).forEach(key => {
      delete cache[key];
    });
  }, [cache]);

  // Función genérica para realizar peticiones
  const request = useCallback(async <T = any>(
    url: string,
    options: RequestOptions = {}
  ): Promise<T> => {
    const {
      method = 'GET',
      headers = {},
      body,
      params,
      cache: useCache = false,
      timeout = 30000
    } = options;

    // Construir URL completa
    const fullUrl = url.startsWith('http')
      ? url
      : buildUrl(url, params);

    // Verificar caché
    const cacheKey = `${method}:${fullUrl}`;
    if (method === 'GET' && useCache && cache[cacheKey]) {
      return cache[cacheKey] as T;
    }

    // Preparar headers
    const requestHeaders = {
      ...defaultHeaders,
      ...headers
    };

    // Preparar opciones de fetch
    const fetchOptions: RequestInit = {
      method,
      headers: requestHeaders,
      body: body ? JSON.stringify(body) : undefined
    };

    // Crear controlador de timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    fetchOptions.signal = controller.signal;

    // Incrementar contador de peticiones
    incrementActiveRequests();

    try {
      // Realizar petición
      const response = await fetch(fullUrl, fetchOptions);

      // Parsear respuesta
      let data: T;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text() as unknown as T;
      }

      // Guardar en caché si es necesario
      if (method === 'GET' && useCache) {
        cache[cacheKey] = data;
      }

      // Manejar errores HTTP
      if (!response.ok) {
        const errorMessage = typeof data === 'object' && data && 'message' in data
          ? (data as any).message
          : DEFAULT_ERROR_MESSAGES.server;

        throw new Error(errorMessage);
      }

      return data;
    } catch (error) {
      // Manejar errores de red
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(DEFAULT_ERROR_MESSAGES.network);
        }
        throw error;
      }

      throw new Error(DEFAULT_ERROR_MESSAGES.unknown);
    } finally {
      // Limpiar timeout y decrementar contador
      clearTimeout(timeoutId);
      decrementActiveRequests();
    }
  }, [buildUrl, cache, defaultHeaders, incrementActiveRequests, decrementActiveRequests]);

  // Métodos específicos para cada tipo de petición
  const get = useCallback(<T = any>(
    url: string,
    params?: Record<string, any>,
    options?: Omit<RequestOptions, 'method' | 'params'>
  ): Promise<T> => {
    return request<T>(url, { ...options, method: 'GET', params });
  }, [request]);

  const post = useCallback(<T = any>(
    url: string,
    data?: any,
    options?: Omit<RequestOptions, 'method' | 'body'>
  ): Promise<T> => {
    return request<T>(url, { ...options, method: 'POST', body: data });
  }, [request]);



  // Valor del contexto
  const contextValue: ApiContextType = {
    isLoading,
    globalError,
    baseUrl,
    endpoints: API_ROUTES,
    request,
    get,
    post,
    buildUrl,
    clearCache,
    setGlobalError
  };

  return (
    <ApiContext.Provider value={contextValue}>
      {children}
    </ApiContext.Provider>
  );
};

export default ApiProvider;
