// ----------------------------------------------
// Theme Variables Configuration
// ----------------------------------------------

// header
$headerHeight: 60px;
$linkSpacer: 15px 15px;
$linkFontSize: 22px;

// sidebar
$sidebar-link-padding: 12px 15px;
$sidebar-icon-size: 23px;
$sidebar-first-level-padding: 0 0 10px 0;
$sidebar-first-level-link-padding: 10px 15px;
$sidebar-first-level-icon-size: 14px;
$sidebar-width-full: 260px;
$sidebar-width-iconbar: 180px;
$sidebar-width-mini: 72px;
$sidebar-spacing-x: 0 24px;
$link-disabled: 0.38;

// user profile
$user-profile-img-size: 85px;
$user-profile-img-margin-left: 30px;
$user-profile-img-padding: 25px 0;
$user-profile-text-padding: 3px 20px;
$user-profile-text-bg: rgba(0, 0, 0, 0.5);
$user-profile-text-max-width: #{$sidebar-width-full}; 
$user-profile-text-width-full: 100%;
$user-profile-dropdown-width: 200px;
$user-profile-dropdown-left: 15px;
$user-profile-dropdown-border-radius: 8px;
$user-profile-dropdown-box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
$user-profile-dropdown-border: 1px solid rgba(0, 0, 0, 0.05);

$boxedWidth: 1300px;
$boxed-margin-x: 50px;  // Restaurado al valor original
$boxed-sidebar-margin: 30px;  // Restaurado al valor original

// horizontal
$horizontal-boxed-page-breadcrumb-container-padding-lg: 20px 10px;
$horizontal-pagewrapper-padding-top-lg: 200px;
$horizontal-pagewrapper-padding-top-sm: 115px;
$sidebar-height: 65px;
$sidebar-li-width: 260px;
$sidebar-first-level-width: 400px;
$horizontal-list-shadow: 0 15px 30px rgb(0 0 0 / 12%);

// #############################
// Sticky Left & Right Sidebar
// #############################
$left-part-width: 260px;
$right-part-width: calc(100% - 260px);
$right-part-height: calc(100vh - 129px);
$topbar-height: 60px;
$rtl-sidebar-link-padding: 12px 8px;
$rtl-minisidebar-link-padding: 14px 12px;
$rtl-minisidebar-link-first-level-padding: 10px 12px 10px 35px;
$rtl-sidebar-first-level-link-padding: 10px 20px;
$nav-cap-padding-rtl: 14px 15px 14px 12px;