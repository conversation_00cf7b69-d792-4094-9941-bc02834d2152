
.btn {
    text-transform: capitalize;
    &.bg-primary-subtle {
      &:hover {
        background-color: var(--bs-primary) !important;
        color: var(--bs-white) !important;
      }
    }
  
    &.bg-secondary-subtle {
      &:hover {
        background-color: var(--bs-secondary) !important;
        color: var(--bs-white) !important;
      }
    }
  
    &.bg-warning-subtle {
      &:hover {
        background-color: var(--bs-warning) !important;
        color: var(--bs-white) !important;
      }
    }
  
    &.bg-danger-subtle {
      &:hover {
        background-color: var(--bs-danger) !important;
        color: var(--bs-white) !important;
      }
    }
  
    &.bg-success-subtle {
      &:hover {
        background-color: var(--bs-success) !important;
        color: var(--bs-white) !important;
      }
    }
  
    &.bg-info-subtle {
      &:hover {
        background-color: var(--bs-info) !important;
        color: var(--bs-white) !important;
      }
    }
  }

  .btn-default {
    background-color: var(--bs-primary);
    color: var(--bs-white);
  }

  .btn-circle {
    border-radius: 100%;
    width: 40px;
    height: 40px;
    padding: 5px;
    line-height: 30px;
  
    // Different Size of Buttons
    // #####################################################################
    &.btn-sm {
      width: 35px;
      height: 35px;
      padding: 8px 10px;
      font-size: 14px;
    }
    &.btn-lg {
      width: 50px;
      height: 50px;
      padding: 14px 10px;
      font-size: 18px;
      line-height: 23px;
    }
    &.btn-xl {
      width: 70px;
      height: 70px;
      padding: 14px 15px;
      font-size: 24px;
    }
  }
  
  .btn-white {
    background-color: var(--bs-white);
    color: var(--bs-primary);
    &:hover{
      background-color: var(--bs-white);
      color: var(--bs-primary);
    }
  }
  
  .btn-white-outline {
    background-color: transparent;
    border: 1px solid var(--bs-white);
    color: var(--bs-white);
    &:hover{
      background-color: var(--bs-white);
      color: var(--bs-primary);
    }
  }
  
  .button-group .btn {
    margin-bottom: 12px;
    margin-right: 12px;
  }
  
  .btn .text-active {
    display: none;
  }
  
  .btn.active .text {
    display: none;
  }
  
  .btn.active .text-active {
    display: inline-block;
  }
  
  .btn-circle.btn-lg, .btn-group-lg>.btn-circle.btn {
    width: 50px;
    height: 50px;
    padding: 14px 10px;
    font-size: 18px;
    line-height: 23px;
  }
  
  .sr-only {
    border: 0;
    clip: rect(0, 0, 0, 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }
