import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context';
import { AUTH_CONFIG } from '@/config/authConfig';
import logger from '@/utils/logger';

interface TokenVerifierProps {
  children: React.ReactNode;
}

const TokenVerifier: React.FC<TokenVerifierProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  logger.info('TOKEN_VERIFIER', `Checking token for route: ${location.pathname}`);
  logger.info('TOKEN_VERIFIER', `User is authenticated: ${isAuthenticated}`);

  if (!isAuthenticated) {
    logger.info('TOKEN_VERIFIER', `User not authenticated, redirecting to login`);
    logger.info('TOKEN_VERIFIER', `Current location: ${location.pathname}, redirecting to: ${AUTH_CONFIG.loginPath}`);
    return <Navigate to={AUTH_CONFIG.loginPath} state={{ from: location }} replace />;
  }

  logger.info('TOKEN_VERIFIER', `Token is valid, rendering route: ${location.pathname}`);
  return <>{children}</>;
};

export default TokenVerifier;