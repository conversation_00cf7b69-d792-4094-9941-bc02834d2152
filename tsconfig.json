{"compilerOptions": {"target": "ES2022", "useDefineForClassFields": true, "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNEXT", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@assets/*": ["./src/assets/*"], "@config/*": ["./src/config/*"], "@context/*": ["./src/context/*"], "@components/*": ["./src/components/*"], "@pages/*": ["./src/pages/*"], "@routes/*": ["./src/routes/*"], "@utils/*": ["./src/utils/*"], "@hoc/*": ["./src/hoc/*"], "@services/*": ["./src/services/*"], "@api/*": ["./src/api/*"], "@hooks/*": ["./src/hooks/*"]}, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true}, "include": ["src", "vite.config.ts"], "exclude": ["node_modules", "dist"]}