
interface LoggerConfig {
  enabled: boolean;
  level: 'debug' | 'info' | 'warn' | 'error';
}

let config: LoggerConfig = {
  enabled: process.env.NODE_ENV === 'development',
  level: 'info'
};

const LOG_LEVELS = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3
};

export const configureLogger = (newConfig: Partial<LoggerConfig>): void => {
  config = { ...config, ...newConfig };
};

export const debug = (module: string, message: string, data?: any): void => {
  if (config.enabled && LOG_LEVELS[config.level] <= LOG_LEVELS.debug) {
    console.debug(`[${module}] ${message}`, data !== undefined ? data : '');
  }
};

export const info = (module: string, message: string, data?: any): void => {
  if (config.enabled && LOG_LEVELS[config.level] <= LOG_LEVELS.info) {
    console.log(`[${module}] ${message}`, data !== undefined ? data : '');
  }
};

export const warn = (module: string, message: string, data?: any): void => {
  if (config.enabled && LOG_LEVELS[config.level] <= LOG_LEVELS.warn) {
    console.warn(`[${module}] ${message}`, data !== undefined ? data : '');
  }
};

export const error = (module: string, message: string, data?: any): void => {
  if (config.enabled && LOG_LEVELS[config.level] <= LOG_LEVELS.error) {
    console.error(`[${module}] ${message}`, data !== undefined ? data : '');
  }
};

const logger = {
  debug,
  info,
  warn,
  error,
  configureLogger
};

export default logger;