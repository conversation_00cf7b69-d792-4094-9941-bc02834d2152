import React, { useEffect } from 'react';

const Scripts: React.FC = () => {
  useEffect(() => {
    const loadScript = (src: string) => {
      const script = document.createElement('script');
      script.src = src;
      script.async = true;
      document.body.appendChild(script);
    };

    loadScript('/node_modules/bootstrap/dist/js/bootstrap.bundle.min.js');
    loadScript('/node_modules/simplebar/dist/simplebar.min.js');
    loadScript('/assets/js/theme/app.init.js');
    loadScript('/assets/js/theme/theme.js');
    loadScript('/assets/js/theme/app.min.js');
    loadScript('/assets/js/theme/sidebarmenu.js');
    loadScript('/assets/js/theme/feather.min.js');
    loadScript('https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js');

    return () => {
      document.querySelectorAll('script').forEach(script => {
        if (script.src.includes('bootstrap') ||
            script.src.includes('simplebar') ||
            script.src.includes('theme') ||
            script.src.includes('iconify')) {
          script.remove();
        }
      });
    };
  }, []);

  return null;
};

export default Scripts;