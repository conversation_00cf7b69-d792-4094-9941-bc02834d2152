import React, { useState } from 'react';
import { PageMetadata } from '@/hoc/withPageMetadata';
import { PERMISSIONS, AUTH_CONFIG } from '@/config/authConfig';
import { useResponsive } from '@/hooks';
import { importedAssets } from '@/config/assetConfig';

const ForgotPassword: React.FC & { pageMetadata: PageMetadata } = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      setError('Por favor, ingresa tu correo electrónico');
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Por favor, ingresa un correo electrónico válido');
      return;
    }
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      setSuccess('Se ha enviado un correo electrónico con instrucciones para restablecer tu contraseña');
      setEmail('');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Error al enviar el correo electrónico');
    } finally {
      setIsLoading(false);
    }
  };
  const { isMobile, isTablet } = useResponsive();
  const showBackground = !isMobile() && !isTablet();
  return (
    <div className="position-relative overflow-hidden radial-gradient min-vh-100 w-100">
      <div className="position-relative z-index-5">
        <div className="row">
          <div className="col-xl-5 col-xxl-4">
            <div className="authentication-login min-vh-100 bg-body row justify-content-center">
              <div className="col-12 text-center">
                <a href={AUTH_CONFIG.homePath} className="text-nowrap logo-img d-flex align-items-center justify-content-center gap-2 px-4 py-9 w-100">
                  <span className="logo-icon">
                    <img src={importedAssets.rayco.swooshDark} alt="logo" className="dark-logo" width="100" />
                  </span>
                  <span className="logo-icon">
                    <img src={importedAssets.rayco.mercosurLogo} alt="logo" className="dark-logo ps-2" width="100" />
                  </span>
                  <span className="logo-icon">
                    <img src={importedAssets.rayco.adidasBarDark} alt="logo" className="dark-logo ps-2" width="100" />
                  </span>
                </a>
              </div>
              <div className="auth-max-width col-sm-8 col-md-6 col-xl-7 px-4">
                <h2 className="mb-1 fs-7 fw-bolder">¿Olvidaste tu contraseña?</h2>
                <p className="mb-7">Ingresa tu correo electrónico y te enviaremos instrucciones para restablecerla</p>

                {/* Formulario de recuperación */}
                <form onSubmit={handleSubmit}>
                  {/* Mensaje de error */}
                  {error && (
                    <div className="alert alert-danger" role="alert">
                      {error}
                    </div>
                  )}

                  {/* Mensaje de éxito */}
                  {success && (
                    <div className="alert alert-success" role="alert">
                      {success}
                    </div>
                  )}

                  {/* Campo de email */}
                  <div className="mb-4">
                    <label htmlFor="email" className="form-label">Correo electrónico</label>
                    <input
                      type="email"
                      className="form-control"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled={isLoading}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  {/* Botón de envío */}
                  <button
                    type="submit"
                    className="btn btn-primary w-100 py-8 mb-4"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Enviando...
                      </>
                    ) : 'Enviar instrucciones'}
                  </button>

                  {/* Enlace de regreso */}
                  <div className="d-flex align-items-center justify-content-center">
                    <a className="text-primary fw-medium" href="/auth/login">
                      <i className="ti ti-arrow-left me-1"></i> Volver a inicio de sesión
                    </a>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {/* Imagen de fondo (solo en pantallas grandes) */}
          {showBackground && (
            <div className="col-xl-7 col-xxl-8">
              <div className="d-flex align-items-center justify-content-center h-100" style={{backgroundColor: '#e5ecf9'}}>
                <img src={importedAssets.backgrounds.login} alt="Auth background" className="img-fluid" />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

ForgotPassword.pageMetadata = {
  title: 'Recuperar Contraseña',
  description: 'Solicitar restablecimiento de contraseña',
  requiresAuth: false,
  path: '/auth/forgot-password',
  showInMenu: false,
  permissions: {
    requiredPermission: PERMISSIONS.NONE,
    resourceId: 'forgot-password'
  }
};

export default ForgotPassword;
