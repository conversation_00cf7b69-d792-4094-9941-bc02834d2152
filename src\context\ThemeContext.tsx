
import React, { createContext, useContext, useCallback } from 'react';
import {
  defaultThemeSettings,
  ThemeMode,
  ColorTheme,
  STORAGE_KEYS,
  themeColors,
  themeCssVariables,
  themeClasses
} from '../config';
import assetConfig from '../config/assetConfig';
import { useSettings, useDOMAttributes, createDocumentAttribute } from '../hooks';


const applyThemeVariables = (colorTheme: ColorTheme, themeMode: ThemeMode): void => {

  if (!themeColors) {
    console.warn('themeColors no está definido. No se pueden aplicar variables CSS de colores.');
    return;
  }
  const colors = themeColors[colorTheme];
  if (!themeCssVariables) {
    console.warn('themeCssVariables no está definido. No se pueden aplicar variables CSS del tema.');
    return;
  }
  const cssVars = themeCssVariables[themeMode];

  if (colors) {
    Object.entries(colors).forEach(([key, value]) => {
      document.documentElement.style.setProperty(`--bs-${key}`, value);
    });
  }

  if (cssVars) {
    document.documentElement.style.setProperty('--body-bg', cssVars.bodyBg);
    document.documentElement.style.setProperty('--card-bg', cssVars.cardBg);
    document.documentElement.style.setProperty('--text-color', cssVars.textColor);
    document.documentElement.style.setProperty('--border-color', cssVars.borderColor);
  }
};


const applyThemeClasses = (settings: ThemeSettings): void => {
  const { theme, colorTheme } = settings;

  if (!themeClasses) {
    console.warn('themeClasses no está definido. No se pueden aplicar clases CSS.');
    return;
  }

  if (themeClasses.light) document.body.classList.remove(themeClasses.light);
  if (themeClasses.dark) document.body.classList.remove(themeClasses.dark);
  const themeClass = theme === 'light' ? themeClasses.light : themeClasses.dark;
  
  if (themeClass) document.body.classList.add(themeClass);
  if (themeClasses.themePrefix) {
    const themeColorClass = `${themeClasses.themePrefix}${colorTheme.toLowerCase()}`;
    const classesToRemove: string[] = [];
    document.body.classList.forEach(className => {
      if (className.startsWith(themeClasses.themePrefix)) {
        classesToRemove.push(className);
      }
    });
    classesToRemove.forEach(className => {
      document.body.classList.remove(className);
    });
    document.body.classList.add(themeColorClass);
  }
};

export interface ThemeSettings {
  theme: ThemeMode;
  colorTheme: ColorTheme;
  cardBorder: boolean;
}

export interface ThemeModeFeatures {
  isLight: boolean;
  isDark: boolean;
  current: ThemeMode;
  toggle: () => void;
  set: (mode: ThemeMode) => void;
}

export interface ColorThemeFeatures {
  current: ColorTheme;
  set: (theme: ColorTheme) => void;
}

export interface CardsFeatures {
  hasBorder: boolean;
  toggleBorder: () => void;
}

export interface ResourcesFeatures {
  getLogo: (type?: 'icon' | 'text') => string;
  getAssetPath: (path: string) => string;
}

export interface ThemeContextType {
  settings: ThemeSettings;
  updateSettings: (newSettings: Partial<ThemeSettings>) => void;
  resetToDefaults: () => void;
  toggleTheme: () => void;
  setTheme: (theme: ThemeMode) => void;
  toggleCardBorder: () => void;
  setColorTheme: (theme: ColorTheme) => void;
  getLogoPath: (type?: 'icon' | 'text') => string;
  getAssetPath: (path: string) => string;
  isMounted: boolean;
  themeMode: ThemeModeFeatures;
  colorTheme: ColorThemeFeatures;
  cards: CardsFeatures;
  resources: ResourcesFeatures;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const handleSettingsChange = useCallback((settings: ThemeSettings) => {
    applyThemeVariables(settings.colorTheme, settings.theme);
    applyThemeClasses(settings);
  }, []);

  const {
    settings,
    updateSettings,
    resetToDefaults,
    isLoaded: mounted
  } = useSettings<ThemeSettings>({
    storageKey: STORAGE_KEYS.THEME,
    defaultSettings: defaultThemeSettings,
    onSettingsChange: handleSettingsChange
  });

  useDOMAttributes({
    attributes: [
      createDocumentAttribute('data-bs-theme', settings.theme),
      createDocumentAttribute('data-color-theme', settings.colorTheme),
      createDocumentAttribute('data-card', settings.cardBorder ? 'border' : 'shadow')
    ],
    dependencies: [settings.theme, settings.colorTheme, settings.cardBorder]
  });

  const toggleTheme = useCallback(() => {
    updateSettings({ theme: settings.theme === 'light' ? 'dark' : 'light' });
  }, [settings.theme, updateSettings]);

  const setTheme = useCallback((theme: ThemeMode) => {
    updateSettings({ theme });
  }, [updateSettings]);

  const toggleCardBorder = useCallback(() => {
    updateSettings({ cardBorder: !settings.cardBorder });
  }, [settings.cardBorder, updateSettings]);

  const setColorTheme = useCallback((theme: ColorTheme) => {
    updateSettings({ colorTheme: theme });
  }, [updateSettings]);

  const getLogoPath = useCallback((type: 'icon' | 'text' = 'icon'): string => {
    return assetConfig.getLogoPath(type, settings.theme);
  }, [settings.theme]);

  const getAssetPath = useCallback((path: string) => {
    return assetConfig.getAssetPath(path);
  }, []);

  const themeMode: ThemeModeFeatures = {
    isLight: settings.theme === 'light',
    isDark: settings.theme === 'dark',
    current: settings.theme,
    toggle: toggleTheme,
    set: setTheme
  };

  const colorThemeFeatures: ColorThemeFeatures = {
    current: settings.colorTheme,
    set: setColorTheme
  };

  const cards: CardsFeatures = {
    hasBorder: settings.cardBorder,
    toggleBorder: toggleCardBorder
  };

  const resources: ResourcesFeatures = {
    getLogo: getLogoPath,
    getAssetPath: getAssetPath
  };

  const contextValue: ThemeContextType = {
    settings,
    updateSettings,
    resetToDefaults,
    toggleTheme,
    setTheme,
    toggleCardBorder,
    setColorTheme,
    getLogoPath,
    getAssetPath,
    isMounted: mounted,
    themeMode,
    colorTheme: colorThemeFeatures,
    cards,
    resources
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme debe usarse dentro de un ThemeProvider');
  }
  return context;
};