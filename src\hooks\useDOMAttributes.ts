import { useEffect, useCallback } from 'react';

export type DOMAttributeTarget = 'documentElement' | 'body';

export interface DOMAttribute {
  target: DOMAttributeTarget;
  name: string;
  value: string;
}

export interface UseDOMAttributesOptions {
  attributes: DOMAttribute[];
  dependencies?: any[];
}

export const useDOMAttributes = ({ attributes, dependencies = [] }: UseDOMAttributesOptions) => {
  const applyAttributes = useCallback(() => {
    attributes.forEach(({ target, name, value }) => {
      const element = target === 'documentElement' ? document.documentElement : document.body;
      element.setAttribute(name, value);
    });
  }, [attributes]);

  const removeAttributes = useCallback(() => {
    attributes.forEach(({ target, name }) => {
      const element = target === 'documentElement' ? document.documentElement : document.body;
      element.removeAttribute(name);
    });
  }, [attributes]);

  useEffect(() => {
    applyAttributes();
    
    return () => {
      // Cleanup is optional - usually we want to keep the attributes
      // removeAttributes();
    };
  }, [...dependencies, applyAttributes]);

  return {
    applyAttributes,
    removeAttributes
  };
};

export const createDOMAttribute = (
  target: DOMAttributeTarget,
  name: string,
  value: string
): DOMAttribute => ({
  target,
  name,
  value
});

export const createDocumentAttribute = (name: string, value: string): DOMAttribute =>
  createDOMAttribute('documentElement', name, value);

export const createBodyAttribute = (name: string, value: string): DOMAttribute =>
  createDOMAttribute('body', name, value);

export default useDOMAttributes;
