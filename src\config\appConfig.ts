import themeConfig, * as theme from './themeConfig';
import responsiveConfig, * as responsive from './responsiveConfig';
import apiConfig, * as api from './apiConfig';
import animationConfig, * as animation from './animationConfig';


export type ThemeMode = theme.ThemeMode;
export type ColorTheme = theme.ColorTheme;
export type LayoutType = 'vertical' | 'horizontal';
export type DirectionType = 'ltr' | 'rtl';
export type SidebarType = 'full' | 'mini-sidebar';
export type Breakpoint = responsive.Breakpoint;
import { PERMISSIONS } from './authConfig';


export const STORAGE_KEYS = {
  CONFIG: 'app_config',
  THEME: theme.THEME_STORAGE_KEY,
  LAYOUT: 'app_layout_settings',
  USER: 'app_user_info',
  AUTH_TOKEN: 'auth_token',
  PERMISSIONS: 'app_user_permissions',
  LANGUAGE: 'app_language',
  RECENT_SEARCHES: 'app_recent_searches',
  FAVORITES: 'app_favorites'
};

export const BREAKPOINTS = responsive.BREAKPOINTS;
export const RESPONSIVE = responsive.RESPONSIVE;
export const DIMENSIONS = responsive.DIMENSIONS;
export const DEFAULT_SETTINGS = {
  theme: theme.defaultThemeSettings.theme,
  colorTheme: theme.defaultThemeSettings.colorTheme,
  cardBorder: theme.defaultThemeSettings.cardBorder,
  layout: 'vertical' as LayoutType,
  direction: 'ltr' as DirectionType,
  boxedLayout: false,
  sidebarType: 'full' as SidebarType,
  sidebarVisible: true,
  language: import.meta.env.VITE_DEFAULT_LANGUAGE || 'es',
  animations: true,
  notifications: true
};

export const defaultThemeSettings = theme.defaultThemeSettings;
export const defaultLayoutSettings = {
  layout: DEFAULT_SETTINGS.layout,
  direction: DEFAULT_SETTINGS.direction,
  boxedLayout: DEFAULT_SETTINGS.boxedLayout,
  sidebarType: DEFAULT_SETTINGS.sidebarType,
  sidebarVisible: DEFAULT_SETTINGS.sidebarVisible
};

export const themeColors = theme.themeColors;
export const themeCssVariables = theme.themeCssVariables;
export const themeClasses = theme.themeClasses;
export const apiEndpoints = api.apiEndpoints;
export const API_ROUTES = api.API_ROUTES;
export const HTTP_STATUS = api.HTTP_STATUS;
export const AUTH_CONFIG = {
  tokenKey: STORAGE_KEYS.AUTH_TOKEN,
  userKey: STORAGE_KEYS.USER,
  loginPath: API_ROUTES.auth.login,
  logoutPath: API_ROUTES.auth.logout,
  homePath: `/main/dashboard`,
  unauthorizedPath: '/auth/unauthorized',
  sessionTimeout: 3600000,
  refreshThreshold: 300000
};

export const ROLES_CONFIG = {
  roles: {
    admin: {
      name: 'Administrador',
      level: PERMISSIONS.VIEW | PERMISSIONS.EDIT | PERMISSIONS.CREATE | PERMISSIONS.DELETE // 15
    },
    manager: {
      name: 'Gerente',
      level: PERMISSIONS.VIEW | PERMISSIONS.EDIT | PERMISSIONS.CREATE // 7
    },
    user: {
      name: 'Usuario',
      level: PERMISSIONS.VIEW | PERMISSIONS.EDIT // 3
    },
    guest: {
      name: 'Invitado',
      level: PERMISSIONS.VIEW // 1
    }
  },
  defaultRole: 'guest'
};

export const useAppConfig = () => {
  const getValue = (key: string, defaultValue: any = '') => {
    try {
      const value = localStorage.getItem(`app_config_${key}`);
      return value !== null ? JSON.parse(value) : defaultValue;
    } catch (error) {
      console.error(`Error getting config value for ${key}:`, error);
      return defaultValue;
    }
  };

  const setValue = (key: string, value: any) => {
    try {
      localStorage.setItem(`app_config_${key}`, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting config value for ${key}:`, error);
    }
  };

  return { getValue, setValue };
};


export const animationUtils = animation.animationUtils;
export const apiUtils = api.apiUtils;
export const responsiveUtils = responsive.responsiveUtils;

const appConfig = {
  theme: themeConfig,
  responsive: responsiveConfig,
  api: apiConfig,
  animation: animationConfig,
  defaultThemeSettings,
  defaultLayoutSettings,
  themeColors,
  themeCssVariables,
  themeClasses,
  apiEndpoints,
  API_ROUTES,
  HTTP_STATUS,
  AUTH_CONFIG,
  ROLES_CONFIG,
  STORAGE_KEYS,
  BREAKPOINTS,
  RESPONSIVE,
  DIMENSIONS,
  DEFAULT_SETTINGS,
  utils: {
    animation: animationUtils,
    api: apiUtils,
    responsive: responsiveUtils
  }
};

export default appConfig;
