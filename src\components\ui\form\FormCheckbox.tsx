
import React, { forwardRef } from 'react';
import classNames from 'classnames';

export interface FormCheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange'> {
  id?: string;
  name: string;
  label?: React.ReactNode;
  helpText?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  checked?: boolean;
  variant?: 'default' | 'switch';
  containerClassName?: string;
  inputClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
  helpClassName?: string;
  onChange?: (checked: boolean) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
}

const FormCheckbox = forwardRef<HTMLInputElement, FormCheckboxProps>(
  (
    {
      id,
      name,
      label,
      helpText,
      error,
      required = false,
      disabled = false,
      checked = false,
      variant = 'default',
      containerClassName,
      inputClassName,
      labelClassName,
      errorClassName,
      helpClassName,
      onChange,
      onFocus,
      onBlur,
      ...rest
    },
    ref
  ) => {
    // Generar ID si no se proporciona
    const checkboxId = id || `form-checkbox-${name}`;

    // Manejar cambio de valor
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e.target.checked);
      }
    };

    return (
      <div className={classNames('mb-3', containerClassName)}>
        <div className={classNames(
          'form-check',
          variant === 'switch' ? 'form-switch' : '',
          'd-block'
        )}>
          <input
            ref={ref}
            type="checkbox"
            id={checkboxId}
            name={name}
            checked={checked}
            disabled={disabled}
            required={required}
            onChange={handleChange}
            onFocus={onFocus}
            onBlur={onBlur}
            className={classNames(
              'form-check-input',
              error ? 'is-invalid' : '',
              inputClassName
            )}
            {...rest}
          />
          {label && (
            <label
              htmlFor={checkboxId}
              className={classNames(
                'form-check-label',
                disabled ? 'text-muted' : '',
                labelClassName
              )}
            >
              {label}
            </label>
          )}
        </div>

        {error && (
          <div
            className={classNames(
              'invalid-feedback d-block',
              errorClassName
            )}
          >
            {error}
          </div>
        )}

        {helpText && (
          <div className={classNames('form-text', helpClassName)}>
            {helpText}
          </div>
        )}
      </div>
    );
  }
);

FormCheckbox.displayName = 'FormCheckbox';

export default FormCheckbox;
