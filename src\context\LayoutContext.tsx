import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import {
  LayoutType,
  DirectionType,
  SidebarType,
  DEFAULT_SETTINGS,
  BREAKPOINTS,
  STORAGE_KEYS,
  RESPONSIVE
} from '@/config';
import { useSettings, useDOMAttributes, createDocumentAttribute, createBodyAttribute } from '@/hooks';

const LAYOUT_STORAGE_KEY = STORAGE_KEYS.LAYOUT;

export interface LayoutSettings {
  layout: LayoutType;
  sidebarType: SidebarType;
  sidebarVisible: boolean;
  boxedLayout: boolean;
  direction: DirectionType;
}

export interface LayoutFeatures {
  isVertical: boolean;
  isHorizontal: boolean;
  isBoxed: boolean;
  isRTL: boolean;
  isLTR: boolean;
  setVertical: () => void;
  setHorizontal: () => void;
  toggleBoxed: () => void;
  toggleDirection: () => void;
  setDirection: (direction: DirectionType) => void;
  setLayout: (layout: LayoutType) => void;
}

export interface SidebarFeatures {
  isFull: boolean;
  isMini: boolean;
  isVisible: boolean;
  toggle: () => void;
  toggleVisibility: () => void;
  setType: (type: SidebarType) => void;
  setVisibility: (visible: boolean) => void;
  handleResponsive: () => void;
}

type Breakpoint = keyof typeof BREAKPOINTS;

export interface LayoutContextType {
  settings: LayoutSettings;
  updateSettings: (newSettings: Partial<LayoutSettings>) => void;
  resetToDefaults: () => void;
  toggleDirection: () => void;
  setDirection: (direction: DirectionType) => void;
  toggleBoxedLayout: () => void;
  setLayout: (layout: LayoutType) => void;
  toggleSidebar: () => void;
  toggleSidebarVisibility: () => void;
  setSidebarType: (type: SidebarType) => void;
  setSidebarVisibility: (visible: boolean) => void;
  handleSidebarResponsive: () => void;
  layout: LayoutFeatures;
  sidebar: SidebarFeatures;
  windowWidth: number;
  windowHeight: number;
  isMobile: boolean;
  isGreaterThan: (breakpoint: Breakpoint) => boolean;
  isLessThan: (breakpoint: Breakpoint) => boolean;
  isBetween: (minBreakpoint: Breakpoint, maxBreakpoint: Breakpoint) => boolean;

  breakpoints: {
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    isXs: boolean;
    isSm: boolean;
    isMd: boolean;
    isLg: boolean;
    isXl: boolean;
    isXxl: boolean;
  };
  breakpointValues: typeof BREAKPOINTS;
}

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);
const defaultLayoutSettings: LayoutSettings = {
  layout: DEFAULT_SETTINGS.layout,
  sidebarType: DEFAULT_SETTINGS.sidebarType,
  sidebarVisible: DEFAULT_SETTINGS.sidebarVisible,
  boxedLayout: DEFAULT_SETTINGS.boxedLayout,
  direction: DEFAULT_SETTINGS.direction
};

export const LayoutProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const {
    settings,
    updateSettings,
    resetToDefaults
  } = useSettings<LayoutSettings>({
    storageKey: LAYOUT_STORAGE_KEY,
    defaultSettings: defaultLayoutSettings
  });

  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [windowHeight, setWindowHeight] = useState(window.innerHeight);
  const [isMobile, setIsMobile] = useState(windowWidth < RESPONSIVE.mobileBreakpoint);

  useDOMAttributes({
    attributes: [
      createDocumentAttribute('data-layout', settings.layout),
      createBodyAttribute('data-sidebartype', settings.sidebarType),
      createDocumentAttribute('data-boxed-layout', settings.boxedLayout ? 'boxed' : 'full'),
      createDocumentAttribute('dir', settings.direction)
    ],
    dependencies: [settings.layout, settings.sidebarType, settings.boxedLayout, settings.direction]
  });

  const isGreaterThan = useCallback((breakpoint: Breakpoint): boolean => {
    return windowWidth >= BREAKPOINTS[breakpoint];
  }, [windowWidth]);

  const isLessThan = useCallback((breakpoint: Breakpoint): boolean => {
    return windowWidth < BREAKPOINTS[breakpoint];
  }, [windowWidth]);

  const isBetween = useCallback((minBreakpoint: Breakpoint, maxBreakpoint: Breakpoint): boolean => {
    return windowWidth >= BREAKPOINTS[minBreakpoint] && windowWidth < BREAKPOINTS[maxBreakpoint];
  }, [windowWidth]);

  const breakpointsInfo = useMemo(() => ({
    isMobile: isLessThan('md'),
    isTablet: isBetween('md', 'xl'),
    isDesktop: isGreaterThan('xl'),
    isXs: isLessThan('sm'),
    isSm: isBetween('sm', 'md'),
    isMd: isBetween('md', 'lg'),
    isLg: isBetween('lg', 'xl'),
    isXl: isBetween('xl', 'xxl'),
    isXxl: isGreaterThan('xxl')
  }), [isGreaterThan, isLessThan, isBetween]);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      setWindowWidth(width);
      setWindowHeight(height);
      setIsMobile(width < RESPONSIVE.mobileBreakpoint);

    };
    window.addEventListener('resize', handleResize);
    handleResize();
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const toggleDirection = useCallback(() => {
    updateSettings({ direction: settings.direction === 'ltr' ? 'rtl' : 'ltr' });
  }, [settings.direction, updateSettings]);

  const setDirection = useCallback((direction: DirectionType) => {
    updateSettings({ direction });
  }, [updateSettings]);

  const toggleBoxedLayout = useCallback(() => {
    updateSettings({ boxedLayout: !settings.boxedLayout });
  }, [settings.boxedLayout, updateSettings]);

  const setLayout = useCallback((layout: LayoutType) => {
    updateSettings({ layout });
  }, [updateSettings]);

  const toggleSidebar = useCallback(() => {
    const mainWrapper = document.getElementById('main-wrapper');
    if (mainWrapper) {
      mainWrapper.classList.toggle('show-sidebar');
      const dataType = document.body.getAttribute('data-sidebartype');
      if (dataType === 'full') {
        document.body.setAttribute('data-sidebartype', 'mini-sidebar');
        updateSettings({ sidebarType: 'mini-sidebar' });
      } else {
        document.body.setAttribute('data-sidebartype', 'full');
        updateSettings({ sidebarType: 'full' });
      }
    }
  }, [updateSettings]);

  const toggleSidebarVisibility = useCallback(() => {
    updateSettings({ sidebarVisible: !settings.sidebarVisible });
  }, [settings.sidebarVisible, updateSettings]);

  const setSidebarType = useCallback((sidebarType: SidebarType) => {
    updateSettings({ sidebarType });
  }, [updateSettings]);

  const setSidebarVisibility = useCallback((sidebarVisible: boolean) => {
    updateSettings({ sidebarVisible });
  }, [updateSettings]);

  const handleSidebarResponsive = useCallback(() => {
    if (windowWidth < RESPONSIVE.sidebarCollapseBreakpoint) {
      const mainWrapper = document.getElementById('main-wrapper');
      if (mainWrapper) {
        mainWrapper.classList.toggle('show-sidebar');
        updateSettings({ sidebarVisible: !settings.sidebarVisible });
      }
    }
    else {
      toggleSidebar();
    }
  }, [windowWidth, settings.sidebarVisible, toggleSidebar, updateSettings]);

  const layout: LayoutFeatures = {
    isVertical: settings.layout === 'vertical',
    isHorizontal: settings.layout === 'horizontal',
    isBoxed: settings.boxedLayout,
    isRTL: settings.direction === 'rtl',
    isLTR: settings.direction === 'ltr',
    setVertical: () => updateSettings({ layout: 'vertical' }),
    setHorizontal: () => updateSettings({ layout: 'horizontal' }),
    toggleBoxed: toggleBoxedLayout,
    toggleDirection,
    setDirection,
    setLayout
  };

  const sidebar: SidebarFeatures = {
    isFull: settings.sidebarType === 'full',
    isMini: settings.sidebarType === 'mini-sidebar',
    isVisible: settings.sidebarVisible,
    toggle: toggleSidebar,
    toggleVisibility: toggleSidebarVisibility,
    setType: setSidebarType,
    setVisibility: setSidebarVisibility,
    handleResponsive: handleSidebarResponsive
  };

  const contextValue: LayoutContextType = {
    settings,
    updateSettings,
    resetToDefaults,
    toggleDirection,
    setDirection,
    toggleBoxedLayout,
    setLayout,
    toggleSidebar,
    toggleSidebarVisibility,
    setSidebarType,
    setSidebarVisibility,
    handleSidebarResponsive,
    layout,
    sidebar,
    windowWidth,
    windowHeight,
    isMobile,
    isGreaterThan,
    isLessThan,
    isBetween,
    breakpoints: breakpointsInfo,
    breakpointValues: BREAKPOINTS
  };

  return (
    <LayoutContext.Provider value={contextValue}>
      {children}
    </LayoutContext.Provider>
  );
};

export const useLayout = () => {
  const context = useContext(LayoutContext);
  if (context === undefined) {
    throw new Error('useLayout debe usarse dentro de un LayoutProvider');
  }
  return context;
};
