import React from 'react';
import { useAuth } from '@/context';
import { PermissionLevel } from '@/config/authConfig';


interface AuthorizeProps {
  resourceId: string;
  requiredPermission: PermissionLevel;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

const Authorize: React.FC<AuthorizeProps> = ({
  resourceId,
  requiredPermission,
  fallback = null,
  children
}) => {
  const { hasPermission } = useAuth();
  if (hasPermission(resourceId, requiredPermission)) {
    return <>{children}</>;
  }
  return <>{fallback}</>;
};

export default Authorize;
