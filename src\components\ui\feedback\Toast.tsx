import React, { useState, useEffect } from 'react';
import classNames from 'classnames';
import { FA6 } from '../../../config/icons/iconUtils';
export type ToastType = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
export interface ToastProps {
  type?: ToastType;
  title?: string;
  message: React.ReactNode;
  duration?: number;
  dismissible?: boolean;
  showIcon?: boolean;
  className?: string;
  onClose?: () => void;
  position?: 'top-start' | 'top-center' | 'top-end' | 'bottom-start' | 'bottom-center' | 'bottom-end';
}

const Toast: React.FC<ToastProps> = ({
  type = 'primary',
  title,
  message,
  duration = 3000,
  dismissible = true,
  showIcon = true,
  className,
  onClose,
  position = 'bottom-end',
}) => {
  const [show, setShow] = useState(true);
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setShow(false);
        if (onClose) {
          onClose();
        }
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  const handleClose = () => {
    setShow(false);
    if (onClose) {
      onClose();
    }
  };

  if (!show) {
    return null;
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <FA6.FaCircleCheck className="text-success" />;
      case 'danger':
        return <FA6.FaCircleExclamation className="text-danger" />;
      case 'warning':
        return <FA6.FaTriangleExclamation className="text-warning" />;
      case 'info':
        return <FA6.FaCircleInfo className="text-info" />;
      default:
        return <FA6.FaCircleInfo className={`text-${type}`} />;
    }
  };

  const getPositionClass = () => {
    switch (position) {
      case 'top-start':
        return 'position-fixed top-0 start-0 m-3';
      case 'top-center':
        return 'position-fixed top-0 start-50 translate-middle-x m-3';
      case 'top-end':
        return 'position-fixed top-0 end-0 m-3';
      case 'bottom-start':
        return 'position-fixed bottom-0 start-0 m-3';
      case 'bottom-center':
        return 'position-fixed bottom-0 start-50 translate-middle-x m-3';
      case 'bottom-end':
        return 'position-fixed bottom-0 end-0 m-3';
      default:
        return '';
    }
  };

  return (
    <div className={getPositionClass()}>
      <div
        className={classNames(
          'toast show',
          `border-${type}`,
          'shadow-sm',
          className
        )}
        style={{ minWidth: '250px' }}
        role="alert"
        aria-live="assertive"
        aria-atomic="true"
      >
        <div className="toast-header d-flex align-items-center">
          {showIcon && (
            <span className="me-2">
              {getIcon()}
            </span>
          )}
          <strong className="me-auto">{title || type.charAt(0).toUpperCase() + type.slice(1)}</strong>
          <small className="text-muted">ahora</small>
          {dismissible && (
            <button
              type="button"
              className="btn-close ms-2"
              aria-label="Close"
              onClick={handleClose}
            ></button>
          )}
        </div>
        <div className="toast-body">{message}</div>
      </div>
    </div>
  );
};

export default Toast;
