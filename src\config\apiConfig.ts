
export interface RequestOptions {
  headers?: Record<string, string>;
  auth?: boolean;
  timeout?: number;
  method?: 'GET' | 'POST';
  body?: any;
}

const buildUrl = (url: string): string => {
  // If it's already a complete URL, return as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // In development mode with proxy enabled, return relative URLs as is
  if (import.meta.env.DEV && import.meta.env.VITE_DISABLE_PROXY !== 'true') {
    return url;
  }

  // Handle URLs with port numbers (for production/non-proxy mode)
  if (url.includes(':')) {
    if (!url.startsWith('http')) {
      const [host, portAndPath] = url.split(':');
      return `http://${host}:${portAndPath}`;
    }
    return url;
  }

  return url;
};

export const sendRequest = async <T = any>(url: string, params?: any, options: RequestOptions = {}): Promise<T> => {
  const {
    headers = {},
    auth = false,
    timeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),
    method = 'POST',
    body = null
  } = options;

  const fullUrl = buildUrl(url);
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...headers
  };

  if (auth) {
    const tokenKey = 'auth_token';
    const token = localStorage.getItem(tokenKey);
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`;
    }
  }

  const requestOptions: RequestInit = {
    method,
    headers: requestHeaders,
    body: body ? JSON.stringify(body) : params ? JSON.stringify(params) : undefined
  };

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  requestOptions.signal = controller.signal;

  try {
    const response = await fetch(fullUrl, requestOptions);
    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data as T;
  } catch (error) {
    clearTimeout(timeoutId);

    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new Error('La solicitud ha excedido el tiempo de espera');
      }
      console.error(`[API] Error in request to ${fullUrl}:`, error.message);
      throw error;
    }
    console.error(`[API] Unknown error in request to ${fullUrl}`);
    throw new Error('Error desconocido al realizar la solicitud');
  }
};

const buildBaseUrl = (baseUrl: string, port: string | number): string => {
  const urlWithProtocol = baseUrl.startsWith('http') ? baseUrl : `http://${baseUrl}`;
  return `${urlWithProtocol}:${port}`;
};

// Helper function to get the correct base URL for development vs production
const getBaseUrl = (path: string): string => {
  // In development mode with proxy enabled, use relative paths
  if (import.meta.env.DEV && import.meta.env.VITE_DISABLE_PROXY !== 'true') {
    return path;
  }
  // In production or when proxy is disabled, use full URLs
  return buildBaseUrl(import.meta.env.VITE_BACKEND_URL, import.meta.env.VITE_BACKEND_PORT) + path;
};

// Endpoints comunes
export const apiEndpoints = {
  // PostgreSQL endpoints
  pg: {
    base: getBaseUrl('/back'),
    handle: getBaseUrl('/back/handle'),
    handleSQL: getBaseUrl('/back/handle/sql'),
  },

  // SQL Server endpoints
  sql: {
    passProc: getBaseUrl('/back/pass/proc'),
    passSQL: getBaseUrl('/back/pass/sql'),
  },

  // API general
  api: getBaseUrl('/api')
};

// Rutas de API específicas
export const API_ROUTES = {
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    register: '/auth/register',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    refreshToken: '/auth/refresh-token'
  },
  dashboard: {
    base: '/dashboard',
  },
  estadodecuenta:{
    base: '/estadodecuenta',
  },
  miperfil:{
    base: '/miperfil',
  }
};

// Códigos de estado HTTP
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
};

// Mensajes de error por defecto
export const DEFAULT_ERROR_MESSAGES = {
  network: 'Error de conexión. Por favor, verifica tu conexión a internet.',
  server: 'Error en el servidor. Por favor, intenta más tarde.',
  unauthorized: 'No autorizado. Por favor, inicia sesión nuevamente.',
  forbidden: 'No tienes permisos para realizar esta acción.',
  notFound: 'El recurso solicitado no existe.',
  validation: 'Error de validación. Por favor, verifica los datos ingresados.',
  unknown: 'Error desconocido. Por favor, intenta nuevamente.'
};

export const apiUtils = {

  formatQueryParams: (params: Record<string, unknown>): string => {
    if (!params || Object.keys(params).length === 0) {
      return '';
    }
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(item => {
            queryParams.append(`${key}[]`, item.toString());
          });
        } else {
          queryParams.append(key, value.toString());
        }
      }
    });

    return `?${queryParams.toString()}`;
  },

  buildUrl: (endpoint: string, params?: Record<string, any>): string => {
    const baseUrl = apiEndpoints.api;
    const fullUrl = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;

    if (!params || Object.keys(params).length === 0) {
      return fullUrl;
    }

    const queryString = apiUtils.formatQueryParams(params);
    return `${fullUrl}${queryString}`;
  },

  get: <T = any>(url: string, params?: any, options: RequestOptions = {}): Promise<T> => {
    return sendRequest<T>(url, params, { ...options, method: 'GET' });
  },

  post: <T = any>(url: string, body?: any, options: RequestOptions = {}): Promise<T> => {
    return sendRequest<T>(url, body, { ...options, method: 'POST' });
  },


};

interface ApiConfig {
  sqlEndpoint: string;
  pgEndpoint: string;
}

const backendSqlUrlDocker = import.meta.env.VITE_BACKEND_SQL_URL_DOCKER;
const backendPgUrlDocker = import.meta.env.VITE_BACKEND_PG_URL_DOCKER;
const backendSqlUrlLocal = import.meta.env.VITE_BACKEND_SQL_URL_COMPLETE;
const backendPgUrlLocal = import.meta.env.VITE_BACKEND_PG_URL_COMPLETE;

const isDocker = import.meta.env.VITE_USE_DOCKER === 'true';

// Create API configuration
const API_CONFIG: ApiConfig = {
  sqlEndpoint: isDocker
    ? backendSqlUrlDocker || 'http://192.168.100.11:8080/ws/handle'
    : backendSqlUrlLocal || 'http://192.168.100.11:8080/ws/handle',
  pgEndpoint: isDocker
    ? backendPgUrlDocker || 'http://192.168.100.11:8010/back/handle'
    : backendPgUrlLocal || 'http://192.168.100.11:8010/back/handle'
};

export const getApiUrl = (endpoint: 'sql' | 'pg'): string => {

  if (import.meta.env.DEV && import.meta.env.VITE_DISABLE_PROXY !== 'true') {
    return endpoint === 'sql' ? '/ws/handle' : '/back/handle';
  }

  return endpoint === 'sql' ? API_CONFIG.sqlEndpoint : API_CONFIG.pgEndpoint;
};

export default {
  sendRequest,
  apiEndpoints,
  API_ROUTES,
  HTTP_STATUS,
  DEFAULT_ERROR_MESSAGES,
  apiUtils,
  API_CONFIG,
  getApiUrl
};
