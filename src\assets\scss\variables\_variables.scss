// ----------------------------------------------
// Variables Style
// ----------------------------------------------

$white: #fff !default;
$gray-100: #f2f4f8 !default;
$gray-200: #e9ecef !default;
$gray-300: #dee2e6 !default;
$gray-400: #ced4da !default;
$gray-500: #768B9E !default;
$gray-600: #6c757d !default;
$gray-700: #4f5467 !default;
$gray-800: #343a40 !default;
$gray-900:  #3A4752!default;
$black: #000 !default;

// Color
$blue: #2cabe3 !default;
$indigo: #6610f2 !default;
$purple: #725AF2 !default;
$pink: #e83e8c !default;
$red: #F8285A !default;
$orange: #fb8c00 !default;
$yellow: #F6C000 !default;
$green: #2cd07e !default;
$teal: #20c997 !default;
$cyan: #26c6da !default;
$primary: #1B84FF !default;
$secondary: #43CED7 !default;
$text-muted: $gray-500 !default;
$inverse: #2f3d4a !default;


// Main Colors
$primary: $primary !default;
$secondary: $secondary !default;
$success: $green !default;
$info: $blue !default;
$warning: $yellow !default;
$danger: $red !default;
$light: $gray-100 !default;
$muted: $gray-500 !default;
$dark: $gray-900 !default;
$dark-light: $gray-600 !default;
$light-gray: $gray-100 !default;
$light-indigo: #c6cdfd !default;
$cyan: $cyan !default;
$orange: $orange !default;


// light colors
$primary-bg-subtle:#EDF5FD !default;
$secondary-bg-subtle: #F2FCFC !default;
$success-bg-subtle: #EDFDF2 !default;
$info-bg-subtle: #e4f5ff !default;
$warning-bg-subtle: #FFFCF0 !default;
$danger-bg-subtle: #FFF0F4 !default;
$light-bg-subtle: #F6F6F6 !default;
$dark-bg-subtle: $gray-600 !default;

// Theme Colorss
$theme-colors: (
  "primary": $primary,
  "secondary": $secondary,
  "success": $success,
  "indigo": $indigo,
  "info": $info,
  "warning": $warning,
  "danger": $danger,
  "light": $light,
  "dark": $dark,
  "muted": $muted,
  "purple": $purple,
  "cyan": $cyan,
  "inverse": $inverse,
  "light-indigo": $light-indigo,
) !default;


$primary-text-emphasis: shade-color($primary, 60%) !default;
$secondary-text-emphasis: shade-color($secondary, 60%) !default;
$success-text-emphasis: shade-color($success, 60%) !default;
$info-text-emphasis: shade-color($info, 60%) !default;
$warning-text-emphasis: shade-color($warning, 60%) !default;
$danger-text-emphasis: shade-color($danger, 60%) !default;
$light-text-emphasis: $gray-100 !default;
$dark-text-emphasis: $gray-600 !default;

// Spacer
$spacer: 1rem !default;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
  6: 12px,
  7: 30px,
  8: 10px,
  9: 20px,
  10: 18px,
  11: 24px,
  12: 30px,
) !default;

// Z-Index Management System
// Centralized z-index values with semantic naming
// Mirrors the TypeScript configuration in responsiveConfig.ts
$z-indexes: (
  content: 10,
  dropdown: 20,
  overlay: 30,
  sidebar: 40,
  header: 50,
  customizer: 99,
  toast: 999,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  dosmil:2000,
  tresmil:3000,
  fullscreen: 9998,
  overnine:9999,
) !default;

$utilities: () !default;
$utilities: (
  "width": (
    property: width,
    class: w,
    values: (
      20: 20%,
      25: 25%,
      30: 30%,
      40: 40%,
      50: 50%,
      60: 60%,
      70: 70%,
      75: 75%,
      85: 85%,
      100: 100%,
      auto: auto,
    ),
  ),
);


// Common
$min-contrast-ratio: 1.7 !default;

// Margins
$enable-negative-margins: true !default;
$enable-shadows: true !default;
$enable-rounded: true !default;

// Global
$text-muted: $muted !default;
$grid-gutter-width: 30px !default;

// Buttons
$btn-padding-y: 8px !default;
$btn-padding-x: 16px !default;
$btn-font-weight: 500 !default;
$btn-border-radius: 30px !default;
$btn-box-shadow: unset !default;
$btn-font-size: 14px !default;
$btn-border-radius-lg: 30px !default;
$btn-border-radius-sm: 30px !default;
$btn-border-width: 1px !default;

// Font Family
$font-size-base: 0.9375rem;
$font-family-sans-serif: "Poppins", sans-serif !default;
$font-weight-medium: 500 !default;
$font-weight-light: 300 !default;
$font-weight-normal: 400 !default;
$font-weight-semibold: 600 !default;
$font-weight-bold: 700 !default;
$font-weight-base: 400 !default;

$h1-font-size: 36px !default;
$h2-font-size: 30px !default;
$h3-font-size: 24px !default;
$h4-font-size: 18px !default;
$h5-font-size: 16px !default;
$h6-font-size: 14px !default;

// Font Sizes
$font-sizes: (
  1: 10px,
  2: 12px,
  3: 14px,
  4: 16px,
  5: 18px,
  6: 20px,
  7: 24px,
  8: 30px,
  9: 36px,
  10: 40px,
  11: 13px,
  12: 15px,
) !default;

$font-size-base: 0.875rem !default;

// display
$display-font-sizes: (
  1: 5rem,
  2: 4.5rem,
  3: 4rem,
  4: 3.5rem,
  5: 3rem,
  6: 2.5rem,
  7: 2rem,
  8: 1.75rem,
) !default;

// Line Heght
$line-height-base: 1.5 !default;
$line-height-sm: 1.25 !default;
$line-height-lg: 2 !default;

// Body
$main-body-bg: #EEF5F9 !default;
$body-bg: #fff !default;
$body-color: #768B9E  !default;

// Heading
$headings-color: $gray-900 !default;
$headings-font-weight: 600 !default;

// Border Color
$border-color: #ebf1f6 !default;
$border-width: 1px !default;

// Box Shadow
$box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03), 0px 0px 1px 0px rgba(0, 0, 0, 0.10) !default;
$box-shadow-sm: 0 0.125rem 0.25rem rgba($black, 0.075) !default;
$box-shadow-lg: 0 1rem 3rem rgba($black, 0.175) !default;
$box-shadow-inset: unset !default;

// Alert
$alert-border-radius: $btn-border-radius;

// Border Radius
$border-radius: 8px !default;
$border-radius-sm: 5px !default;
$border-radius-md: 6px !default;
$border-radius-lg: 12px !default;
$border-radius-xl: 25px !default;

// Card
$card-bg: $white !default;
$card-title-color: $gray-900 !default;
$card-subtitle-color: $text-muted !default;
$card-spacer-y: 30px !default;
$card-spacer-x: 30px !default;
$card-border-width: 0px !default;
$card-border-color: transparent !default;
$card-border-radius: 10px !default;
$card-box-shadow: $box-shadow !default;
$card-group-margin: $spacer * 0.75 !default;

// Badges
$badge-font-size: 14px !default;
$badge-font-weight: $font-weight-normal !default;
$badge-padding-y: 5px !default;
$badge-padding-x: 10px !default;
$badge-border-radius: 30px;

// Progressbar
$progress-height:5px !default;
$progress-bg: #f1f1f1 !default;

// Table
$table-bg:transparent !default;
$table-color:$body-color;
$table-cell-padding-y: 12px !default;
$table-cell-padding-x: 12px !default;
$table-th-font-weight: 500 !default;
$table-striped-bg: $gray-200 !default;
$table-hover-bg: $gray-100 !default;
$table-border-color: $border-color !default;

// Tabs
$nav-link-color: $dark !default;
$nav-link-hover-color: $dark !default;
$nav-tabs-link-active-color: $white !default;
$nav-tabs-link-active-bg: $primary !default;
$nav-tabs-border-radius: 30px !default;
$nav-tabs-border-width: 0 !default;
$nav-link-font-size: 16px !default;
$nav-link-font-weight: $font-weight-normal !default;

// Tooltips
$tooltip-font-size: 12px !default;
$tooltip-bg: $dark;

// Input
$form-label-color: $gray-600;
$form-label-font-weight: 600;
$form-select-indicator-padding: 38px;
$form-select-border-radius: 5px !default;
$form-select-box-shadow: unset !default;
$input-border-radius: 8px !default;
$input-focus-border-color:$dark !default;
$input-group-addon-bg: $gray-200 !default;
$input-group-addon-border-color: $light !default;
$custom-select-indicator: url(../../assets/images/custom-select.png) !default;
$form-select-bg-size:12px 12px; // In pixels because image dimensions
$input-border-color: $border-color !default;
$input-bg: $white !default;
$input-color: $body-color !default;
$input-padding-y: 8px;
$input-padding-x: 16px;
$form-check-input-width: 1.313em;
$form-check-min-height: 1.313em;
$form-check-input-border: 1px solid #dee2e6;
$form-check-input-focus-box-shadow: none !default;
$input-group-addon-border-color: #dee2e6;
$form-check-input-checked-bg-color:var(--bs-primary);

$list-group-bg:transparent !default;
$list-group-border-radius: 8px;

// Paganation
$pagination-active-color: $white !default;
$pagination-active-bg: $primary !default;

// Dropdown
$dropdown-border-radius: 12px !default;
$dropdown-width: 350px !default;
$dropdown-border-width: 0 !default;
$dropdown-item-padding-y: 10px !default;
$dropdown-link-hover-bg: $primary-bg-subtle !default;
$dropdown-link-active-bg: $primary-bg-subtle;
$dropdown-link-active-color: $dark;
$dropdown-bg:$white;
$dropdown-link-color:$dark !default;
$dropdown-box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
$link-color: $dark !default;
$dropdown-spacer: 15px !default;

$link-hover-color: $primary !default;

// Offcanvas
$offcanvas-border-width: 0 !default;
$offcanvas-horizontal-width: 330px !default;
$offcanvas-bg-color:$white !default;

// Modal
$modal-content-border-width: 0 !default;
$modal-content-bg:#fff !default;
$modal-content-box-shadow-xs: 0 0.25rem 0.5rem rgba($black, 0.2) !default;
$modal-content-box-shadow-sm-up: 0 0.5rem 1rem rgba($black, 0.2) !default;

// Breadcrumb
$breadcrumb-font-size: 14px;
$breadcrumb-padding-y: 0;
$breadcrumb-padding-x: 0;
$breadcrumb-item-padding-x: 0.5rem;
$breadcrumb-margin-bottom: 0;
$breadcrumb-divider-color: $gray-500;
$breadcrumb-active-color: $gray-500;
$breadcrumb-divider: quote(">");
$breadcrumb-divider-flipped: $breadcrumb-divider;

$list-group-color:$dark !default;

// Accordion
$accordion-padding-y: 1.25rem;
$accordion-padding-x: 2rem;
$accordion-button-color: $dark;
$accordion-button-bg:$white;
$accordion-bg:$white;
$accordion-body-padding-y: 1rem;
$accordion-border-color: $border-color;
$accordion-button-focus-border-color:transparent !default;
$accordion-button-focus-box-shadow:transparent !default;
$accordion-button-active-color: $dark;
$accordion-icon-color: #0000008a;
$accordion-icon-active-color: #0000008a;

// toast
$toast-border-width: 8px !default;
$toast-border-radius: $card-border-radius;

$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1300px,
  xxl: 1400px,
) !default;


$hr-border-color: $border-color !default;
$hr-opacity: 1 !default;
$form-check-input-focus-box-shadow: unset;
$list-group-bg: var(--bs-card-bg) !default;
