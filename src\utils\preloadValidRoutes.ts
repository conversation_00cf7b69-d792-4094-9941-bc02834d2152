import { pageMetadata } from '@/routes/PageMetadata';
import { MAIN_ROUTE_PREFIX } from '@/routes/routeConstants';

export const validRoutesCache: {
  paths: Set<string>;
  isInitialized: boolean;
} = {
  paths: new Set<string>(),
  isInitialized: false
};

let isPreloading = false;

export const preloadValidRoutes = (): void => {
  if (validRoutesCache.isInitialized && !isPreloading) {
    return;
  }
  if (isPreloading) {
    return;
  }
  isPreloading = true;
  validRoutesCache.paths.clear();
  Object.entries(pageMetadata).forEach(([, metadata]) => {
    if (metadata.path) {
      validRoutesCache.paths.add(metadata.path);
      if (metadata.path.startsWith('/')) {
        validRoutesCache.paths.add(metadata.path.substring(1));
      }
    }
  });

  const defaultRoutes = [
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password',
    '/auth/error',
    '/auth/unauthorized',
    `${MAIN_ROUTE_PREFIX}/dashboard`,
    `${MAIN_ROUTE_PREFIX}/settings`,
    MAIN_ROUTE_PREFIX
  ];

  defaultRoutes.forEach(route => {
    validRoutesCache.paths.add(route);
    if (route.startsWith('/')) {
      validRoutesCache.paths.add(route.substring(1));
    }
  });

  validRoutesCache.isInitialized = true;
  isPreloading = false;
};
export default preloadValidRoutes;