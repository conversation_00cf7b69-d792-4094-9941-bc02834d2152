import { Routes, Route, Navigate } from 'react-router-dom';
import { routes } from '@/routes/PageMetadata';
import { AUTH_ROUTE_PREFIX, ROUTES } from '@/routes/routeConstants';
import Login from '@/pages/Auth/Login';

export const AuthRoutes = () => {
  const authRoutes = routes.filter(route => !route.meta?.requiresAuth && route.path.startsWith(AUTH_ROUTE_PREFIX));

  return (
    <Routes>
      <Route path={ROUTES.LOGIN} element={<Login />} />
      {authRoutes.map(route => (
        <Route
          key={route.key}
          path={route.path}
          element={<route.component />}
        />
      ))}
      <Route path="/" element={<Navigate to={ROUTES.LOGIN} replace />} />
      <Route path={AUTH_ROUTE_PREFIX} element={<Navigate to={ROUTES.LOGIN} replace />} />
      <Route path="*" element={<Navigate to={ROUTES.LOGIN} replace />} />
    </Routes>
  );
};

export default AuthRoutes;