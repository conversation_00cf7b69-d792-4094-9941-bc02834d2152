import React from 'react';
import { Link } from 'react-router-dom';
import { useTheme } from '@/context/ThemeContext';
import { useLayout } from '@/context/LayoutContext';
import assetConfig from '@/config/assetConfig';

const LogoSidebar: React.FC = () => {
  const { getLogoPath } = useTheme();
  const { settings: layoutSettings } = useLayout();

  // Siempre usar la versión light del icono independientemente del tema
  const logoIconSrc = assetConfig.importedAssets.logos.icon.light;

  return (
    <div className="brand-logo d-flex align-items-center justify-content-between">
      <Link to="/" className="text-nowrap logo-img d-flex align-items-center gap-2">
        <b className="logo-icon">
          {/* Logo icon - Always use light version regardless of theme */}
          <img
            src={logoIconSrc}
            alt="homepage"
            className="light-logo"
            width="35"
          />
        </b>
        {/* Logo text - Only show when sidebar is not collapsed */}
        {layoutSettings.sidebarType !== 'mini-sidebar' && (
          <span className="logo-text">
            <img
              src={getLogoPath('text')}
              alt="homepage"
              className="light-logo ps-2"
              width="140"
            />
          </span>
        )}
      </Link>
    </div>
  );
};

export default LogoSidebar;
