import React from 'react';
import { Link } from 'react-router-dom';
import { FA6 } from '@/config/icons/iconUtils';

interface Language {
  code: string;
  name: string;
  flag: string;
}

const DdLanguage: React.FC = () => {
  const languages: Language[] = [
    { code: 'en', name: 'English', flag: '/assets/images/flag/en.png' },
    { code: 'fr', name: 'French', flag: '/assets/images/flag/fr.png' },
    { code: 'de', name: 'German', flag: '/assets/images/flag/de.png' },
    { code: 'es', name: 'Spanish', flag: '/assets/images/flag/es.png' },
    { code: 'pt', name: 'Portuguese', flag: '/assets/images/flag/pt.png' }
  ];

  return (
    <>
      <div className="py-3 px-4 bg-secondary">
        <div className="mb-0 fs-6 fw-medium text-white">Language</div>
        <div className="mb-0 fs-2 fw-medium text-white">Choose your language</div>
      </div>
      <div className="language-body" data-simplebar>
        {languages.map((language) => (
          <Link
            key={language.code}
            to="#"
            className="p-3 d-flex align-items-center dropdown-item gap-3 border-bottom"
          >
            <img
              src={language.flag}
              alt={language.name}
              className="rounded-circle"
              width="24"
              height="24"
            />
            <span className="fs-2">{language.name}</span>
          </Link>
        ))}
      </div>
      <div className="p-3">
        <Link 
          className="d-flex btn btn-secondary align-items-center justify-content-center gap-2" 
          to="/language"
        >
          <span>Check all Languages</span>
          <FA6.FaArrowRight className="fs-6" />
        </Link>
      </div>
    </>
  );
};

export default DdLanguage; 