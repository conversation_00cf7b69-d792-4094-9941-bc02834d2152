import { useAuth } from '@/context/AuthContext';
import { DatosPersonasBL } from '@/context/AuthContext';
import { formatDatosPersonalesBL, fetchDatosPersonalesBL } from '@/services/datosPersonalesService';
import { fetchColaboradorData, fetchColaboradorAttributes } from '@/context/AuthContext';
import { useCallback } from 'react';

export function useDatosPersonales() {
  const { user, updateDatosPersonales } = useAuth();
  const datosPersonales = user?.datosBLaboro;
  const hasDatosPersonales = Boolean(datosPersonales);
  const datosFormateados = datosPersonales ? formatDatosPersonalesBL(datosPersonales) : null;
  const getCampo = (campo: keyof DatosPersonasBL, fallback: string = 'Sin información') => {
    return datosPersonales?.[campo] || fallback;
  };

  const getCodigoCliente = () => {
    return user?.id_ctacte?.toString() || 'Sin información';
  };

  const getLegajoEmpleado = () => {
    return user?.nrolegajo || 'Sin información';
  };

  const getDomicilioLocalidad = () => {
    return getCampo('DomicilioLoc', 'Sin información');
  };

  const getNombreCompleto = () => {
    return getCampo('Nombre', 'Usuario');
  };

  const getDomicilioCompleto = () => {
    const calle = getCampo('DomicilioCalle', '');
    return calle || 'Sin información';
  };

  const getGeneroFormateado = () => {
    const sexo = getCampo('Sexo', '');
    switch (String(sexo).toUpperCase()) {
      case 'M':
        return 'Masculino';
      case 'F':
        return 'Femenino';
      default:
        return sexo || 'Sin especificar';
    }
  };

  const getFechaNacimientoFormateada = () => {
    const fecha = getCampo('FecNacimiento', '');
    if (!fecha) return 'Sin información';
    try {
      const fechaStr = String(fecha);
      let fechaObj: Date;
      if (fechaStr.includes('-')) {
        const [year, month, day] = fechaStr.split('-').map((num: string) => parseInt(num, 10));
        fechaObj = new Date(year, month - 1, day);
      } else {
        fechaObj = new Date(fechaStr);
      }
      return fechaObj.toLocaleDateString('es-AR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting birth date:', error);
      return String(fecha);
    }
  };

  const getEdad = () => {
    const fecha = getCampo('FecNacimiento', '');
    if (!fecha) return null;
    try {
      const fechaStr = String(fecha);
      let fechaNacimiento: Date;
      if (fechaStr.includes('-')) {
        const [year, month, day] = fechaStr.split('-').map((num: string) => parseInt(num, 10));
        fechaNacimiento = new Date(year, month - 1, day);
      } else {
        fechaNacimiento = new Date(fechaStr);
      }
      const hoy = new Date();
      let edad = hoy.getFullYear() - fechaNacimiento.getFullYear();
      const mesActual = hoy.getMonth();
      const mesNacimiento = fechaNacimiento.getMonth();
      if (mesActual < mesNacimiento || (mesActual === mesNacimiento && hoy.getDate() < fechaNacimiento.getDate())) {
        edad--;
      }
      return edad;
    } catch (error) {
      console.error('Error calculating age:', error);
      return null;
    }
  };

  const getTelefonoFormateado = () => {
    const telefono = getCampo('Telefono', '');
    if (!telefono) return 'Sin información';
    return telefono;
  };

  const getEmailPersonal = () => {
    return getCampo('EmailPersonal', 'Sin información');
  };

  const getCUILFormateado = () => {
    const cuil = getCampo('CUIL', '');
    if (!cuil) return 'Sin información';
    const cuilStr = String(cuil);
    if (cuilStr.length === 11 && /^\d+$/.test(cuilStr)) {
      return `${cuilStr.substring(0, 2)}-${cuilStr.substring(2, 10)}-${cuilStr.substring(10)}`;
    }
    return cuilStr;
  };
  const getFechaContratoFormateada = () => {
    const fecha = getCampo('FecIngreso', '');
    if (!fecha) return 'Sin información';
    try {
      const fechaStr = String(fecha);
      let fechaContrato: Date;
      if (fechaStr.includes('-')) {
        const [year, month, day] = fechaStr.split('-').map((num: string) => parseInt(num, 10));
        fechaContrato = new Date(year, month - 1, day);
      } else {
        fechaContrato = new Date(fechaStr);
      }
      return fechaContrato.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting contract date:', error);
      return String(fecha);
    }
  };

  const getLimiteCreditoFormateado = () => {
    const limite = getCampo('LimiteCredito', '');
    if (!limite) return '0.00';
    try {
      const numero = parseFloat(String(limite));
      return numero.toLocaleString('es-ES', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    } catch (error) {
      console.error('Error formatting credit limit:', error);
      return String(limite);
    }
  };

  const getEstado = () => {
    return getCampo('Estado', 'activo') as 'activo' | 'inactivo';
  };

  const getLegajo = () => {
    return getCampo('Codigo', 'Sin información');
  };

  const getFechaContratoRaw = () => {
    return getCampo('FecIngreso', '');
  };

  const getAntiguedad = () => {
    const fecha = getCampo('FecIngreso', '');
    if (!fecha) return '';
    try {
      const fechaStr = String(fecha);
      const today = new Date();
      let contract: Date;
      if (fechaStr.includes('-')) {
        const [year, month, day] = fechaStr.split('-').map((num: string) => parseInt(num, 10));
        contract = new Date(year, month - 1, day);
      } else {
        contract = new Date(fechaStr);
      }
      if (isNaN(contract.getTime())) {
        return '';
      }
      if (contract > today) {
        return "1 mes";
      }
      let years = today.getFullYear() - contract.getFullYear();
      let months = today.getMonth() - contract.getMonth();
      if (today.getDate() < contract.getDate()) {
        months--;
      }
      if (months < 0) {
        years--;
        months += 12;
      }
      if (years === 0 && months === 0) {
        return "1 mes";
      }
      if (years === 0) {
        return months === 1 ? "1 mes" : `${months} meses`;
      } else if (months === 0) {
        return years === 1 ? "1 año" : `${years} años`;
      } else {
        const yearText = years === 1 ? "1 año" : `${years} años`;
        const monthText = months === 1 ? "1 mes" : `${months} meses`;
        return `${yearText}, ${monthText}`;
      }
    } catch (error) {
      console.error('Error calculating seniority:', error);
      return '';
    }
  };

  const refetchDatosPersonales = useCallback(async (): Promise<boolean> => {
    if (!user?.nrodoc) {
      console.warn('[useDatosPersonales] No document number available for refetch');
      return false;
    }

    try {
      let datosBLaboro = await fetchDatosPersonalesBL(user.nrodoc);

      if (datosBLaboro) {
        try {
          const codCtaCte = user.id_ctacte?.toString();
          if (codCtaCte) {
            const colaboradorResponse = await fetchColaboradorData(codCtaCte, user.nrodoc);
            if (colaboradorResponse.success && colaboradorResponse.data && colaboradorResponse.data.length > 0) {
              const colaboradorData = colaboradorResponse.data[0];
              let estado: 'activo' | 'inactivo' = 'activo';

              try {
                const attributesResponse = await fetchColaboradorAttributes(codCtaCte);
                if (attributesResponse.success && attributesResponse.data) {
                  const statusAttribute = attributesResponse.data.find(attr => attr.CODATR === 'C05');
                  if (statusAttribute) {
                    estado = statusAttribute.CODATRVAL === '2' ? 'activo' : 'inactivo';
                  }
                }
              } catch (attributesError) {
                console.warn('[useDatosPersonales] Error fetching colaborador attributes:', attributesError);
              }

              datosBLaboro = {
                ...datosBLaboro,
                FecIngreso: colaboradorData.FecIngreso,
                LimiteCredito: colaboradorData.LimiteCredito,
                Estado: estado
              };
            }
          }
        } catch (colaboradorError) {
          console.warn('[useDatosPersonales] Error fetching colaborador data during refetch:', colaboradorError);
        }

        updateDatosPersonales(datosBLaboro);
        return true;
      } else {
        console.warn(`[useDatosPersonales] No personal data found for document: ${user.nrodoc}`);
        return false;
      }
    } catch (error) {
      console.error('[useDatosPersonales] Error refetching personal data:', error);
      return false;
    }
  }, [user?.nrodoc, user?.id_ctacte, updateDatosPersonales]);

  return {
    datosPersonales,
    datosFormateados,
    hasDatosPersonales,
    getCampo,
    getNombreCompleto,
    getDomicilioCompleto,
    getGeneroFormateado,
    getFechaNacimientoFormateada,
    getEdad,
    getTelefonoFormateado,
    getEmailPersonal,
    getCUILFormateado,
    getFechaContratoFormateada,
    getFechaContratoRaw,
    getAntiguedad,
    getLimiteCreditoFormateado,
    getEstado,
    getLegajo,
    getCodigoCliente,
    getLegajoEmpleado,
    getDomicilioLocalidad,
    nombre: getNombreCompleto(),
    domicilio: getDomicilioCompleto(),
    genero: getGeneroFormateado(),
    fechaNacimiento: getFechaNacimientoFormateada(),
    edad: getEdad(),
    telefono: getTelefonoFormateado(),
    email: getEmailPersonal(),
    cuil: getCUILFormateado(),
    documento: getCampo('DocNro', 'Sin información'),
    codigo: getCodigoCliente(),
    foto: getCampo('Foto', ''),
    fechaContrato: getFechaContratoFormateada(),
    fechaContratoRaw: getFechaContratoRaw(),
    antiguedad: getAntiguedad(),
    limiteCredito: getLimiteCreditoFormateado(),
    estado: getEstado(),
    legajo: getLegajoEmpleado(),
    localidad: getDomicilioLocalidad(),
    refetchDatosPersonales
  };
}
