export const PERMISSIONS = {
  NONE: 0,    // 0000
  VIEW: 1,    // 0001
  EDIT: 2,    // 0010
  CREATE: 4,  // 0100
  DELETE: 8,  // 1000
  FULL: 15,    // 1111
};

export type PermissionLevel = number;
export const AUTH_STORAGE_KEY = 'auth_user';
export const MAIN_ROUTE_PREFIX = import.meta.env.VITE_MAIN_ROUTE_PREFIX || '/main';
export const AUTH_CONFIG = {
  tokenKey: 'auth_token',
  userKey: AUTH_STORAGE_KEY,
  loginPath: '/auth/login',
  logoutPath: '/auth/logout',
  registerPath: '/auth/register',
  forgotPasswordPath: '/auth/forgot-password',
  resetPasswordPath: '/auth/reset-password',
  homePath: `${MAIN_ROUTE_PREFIX}/dashboard`,
  unauthorizedPath: '/auth/unauthorized',
  sessionTimeout: parseInt(import.meta.env.VITE_AUTH_SESSION_TIMEOUT || '3600000'), 
  refreshThreshold: parseInt(import.meta.env.VITE_AUTH_REFRESH_THRESHOLD || '300000'),
  rememberMeEnabled: import.meta.env.VITE_AUTH_REMEMBER_ME_ENABLED !== 'false',
  rememberMeDuration: parseInt(import.meta.env.VITE_AUTH_REMEMBER_ME_DURATION || '30'), 
  maxLoginAttempts: parseInt(import.meta.env.VITE_AUTH_MAX_LOGIN_ATTEMPTS || '5'),
  lockoutDuration: parseInt(import.meta.env.VITE_AUTH_LOCKOUT_DURATION || '15') 
};

// Roles y permisos
export const ROLES_CONFIG = {
  roles: {
    admin: {
      name: 'Administrador',
      level: 15, // VIEW + EDIT + CREATE + DELETE
      description: 'Acceso completo a todas las funcionalidades'
    },
    manager: {
      name: 'Gerente',
      level: 7, // VIEW + EDIT + CREATE
      description: 'Puede editar y crear pero no eliminar'
    },
    user: {
      name: 'Usuario',
      level: 3, // VIEW + EDIT
      description: 'Puede ver y editar información, sin capacidad de crear o eliminar'
    },
    guest: {
      name: 'Invitado',
      level: 1, // VIEW
      description: 'Solo puede ver información, sin capacidad de modificación'
    }
  },
  defaultRole: 'guest'
};

export const TEST_USERS = import.meta.env.VITE_ENABLE_TEST_USERS !== 'false' ? {
  admin: {
    id: '1',
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'admin', // En producción, nunca almacenar contraseñas en texto plano
    role: 'admin',
    permissions: {
      'dashboard': 15, // VIEW + EDIT + CREATE + DELETE
      'colaboradores': 15,
      'settings': 15,
      'blank': 15
    }
  },
  rrhh: {
    id: '2',
    firstName: 'RRHH',
    lastName: 'Staff',
    email: '<EMAIL>',
    password: 'rrhh',
    role: 'manager',
    permissions: {
      'dashboard': 7, // VIEW + EDIT + CREATE
      'colaboradores': 7,
      'settings': 1, // VIEW
      'blank': 1
    }
  },
  user: {
    id: '3',
    firstName: 'Regular',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'user',
    role: 'user',
    permissions: {
      'dashboard': 1, // VIEW
      'colaboradores': 0, // NONE
      'settings': 0,
      'blank': 1
    }
  }
} : {};

export const authUtils = {
  hasPermission: (userPermission: PermissionLevel, requiredPermission: PermissionLevel): boolean => {
    return (userPermission & requiredPermission) === requiredPermission;
  }
};

export default {
  AUTH_CONFIG,
  ROLES_CONFIG,
  TEST_USERS,
  authUtils
};
