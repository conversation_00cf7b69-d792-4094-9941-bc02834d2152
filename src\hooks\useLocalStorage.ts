import { useState, useEffect, useCallback, useRef } from 'react';
export type StorageType = 'localStorage' | 'sessionStorage';
export interface UseLocalStorageOptions {
  expiry?: number;
  serialize?: (value: any) => string;
  deserialize?: (value: string) => any;
  storageType?: StorageType;
  compress?: boolean;
  maxSize?: number;
  onError?: (error: Error) => void;
}

function isStorageAvailable(type: StorageType): boolean {
  try {
    const storage = window[type];
    const testKey = '__storage_test__';
    storage.setItem(testKey, testKey);
    storage.removeItem(testKey);
    return true;
  } catch (e) {
    return false;
  }
}

function compressString(input: string): string {
  if (!input || input.length < 100) return input; 
  try {
    return btoa(input);
  } catch (e) {
    return input;
  }
}

function decompressString(input: string): string {
  if (!input) return input;
  try {
    return atob(input);
  } catch (e) {
    return input;
  }
}

export function useLocalStorage<T>(
  key: string,
  initialValue: T,
  options: UseLocalStorageOptions = {}
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  const {
    expiry,
    serialize = JSON.stringify,
    deserialize = JSON.parse,
    storageType = 'localStorage',
    compress = false,
    maxSize = 1024 * 50, 
    onError = (error: Error) => console.error(error)
  } = options;

  const storageAvailableRef = useRef<boolean>(isStorageAvailable(storageType));

  const getStorage = useCallback((): Storage => {
    return storageType === 'localStorage' ? window.localStorage : window.sessionStorage;
  }, [storageType]);

  const [storedValue, setStoredValue] = useState<T>(() => {
    if (!storageAvailableRef.current) {
      return initialValue;
    }
    try {
      const storage = getStorage();
      const item = storage.getItem(key);
      if (!item) {
        return initialValue;
      }
      const decompressedItem = compress ? decompressString(item) : item;
      const parsedItem = deserialize(decompressedItem);
      if (expiry && parsedItem._expiry && Date.now() > parsedItem._expiry) {
        storage.removeItem(key);
        return initialValue;
      }
      return expiry ? parsedItem.value : parsedItem;
    } catch (error) {
      const errorMessage = `Error reading ${storageType} key "${key}": ${error instanceof Error ? error.message : String(error)}`;
      onError(new Error(errorMessage));
      return initialValue;
    }
  });

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    if (!storageAvailableRef.current) {
      const newValue = value instanceof Function ? value(storedValue) : value;
      setStoredValue(newValue);
      return;
    }
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      let itemToStore: any = valueToStore;
      if (expiry) {
        itemToStore = {
          value: valueToStore,
          _expiry: Date.now() + expiry
        };
      }
      let serializedValue = serialize(itemToStore);
      if (serializedValue.length > maxSize) {
        if (!compress) {
          serializedValue = compressString(serializedValue);
        }
        if (serializedValue.length > maxSize) {
          throw new Error(`Data size exceeds maximum allowed size (${serializedValue.length} > ${maxSize} bytes)`);
        }
      } else if (compress) {
        serializedValue = compressString(serializedValue);
      }
      const storage = getStorage();
      storage.setItem(key, serializedValue);
    } catch (error) {
      if (error instanceof DOMException && (
        error.code === 22 || // Chrome
        error.code === 1014 || // Firefox
        error.name === 'QuotaExceededError' || // Safari
        error.name === 'NS_ERROR_DOM_QUOTA_REACHED' // Firefox
      )) {
        const errorMessage = `Storage quota exceeded for ${storageType}. Try using sessionStorage or reducing data size.`;
        onError(new Error(errorMessage));
      } else {
        const errorMessage = `Error setting ${storageType} key "${key}": ${error instanceof Error ? error.message : String(error)}`;
        onError(new Error(errorMessage));
      }
    }
  }, [key, storedValue, expiry, serialize, compress, maxSize, storageType, getStorage, storageAvailableRef, onError]);

  const removeValue = useCallback(() => {
    if (!storageAvailableRef.current) {
      setStoredValue(initialValue);
      return;
    }
    try {
      const storage = getStorage();
      storage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      const errorMessage = `Error removing ${storageType} key "${key}": ${error instanceof Error ? error.message : String(error)}`;
      onError(new Error(errorMessage));
    }
  }, [key, initialValue, storageType, getStorage, storageAvailableRef, onError]);

  useEffect(() => {
    if (storageType !== 'localStorage') return;
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          const decompressedValue = compress ? decompressString(e.newValue) : e.newValue;
          const newValue = deserialize(decompressedValue);
          setStoredValue(expiry ? newValue.value : newValue);
        } catch (error) {
          const errorMessage = `Error parsing storage change for key "${key}": ${error instanceof Error ? error.message : String(error)}`;
          onError(new Error(errorMessage));
        }
      } else if (e.key === key && e.newValue === null) {
        setStoredValue(initialValue);
      }
    };
    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [key, initialValue, expiry, deserialize, compress, storageType, onError]);
  useEffect(() => {
    if (!expiry) return;
    const checkExpiry = () => {
      try {
        const storage = getStorage();
        const item = storage.getItem(key);
        if (!item) return;
        const decompressedItem = compress ? decompressString(item) : item;
        const parsedItem = deserialize(decompressedItem);
        if (parsedItem._expiry && Date.now() > parsedItem._expiry) {
          storage.removeItem(key);
          setStoredValue(initialValue);
        }
      } catch (error) {
      }
    };
    const intervalId = setInterval(checkExpiry, 60000);
    return () => {
      clearInterval(intervalId);
    };
  }, [key, initialValue, expiry, deserialize, compress, getStorage]);

  return [storedValue, setValue, removeValue];
}

export default useLocalStorage;
