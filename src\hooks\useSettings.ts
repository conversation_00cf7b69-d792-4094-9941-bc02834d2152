import { useState, useEffect, useCallback } from 'react';

export interface UseSettingsOptions<T> {
  storageKey: string;
  defaultSettings: T;
  onSettingsChange?: (settings: T) => void;
}

export interface UseSettingsReturn<T> {
  settings: T;
  updateSettings: (newSettings: Partial<T>) => void;
  resetToDefaults: () => void;
  isLoaded: boolean;
}

export const useSettings = <T extends Record<string, any>>({
  storageKey,
  defaultSettings,
  onSettingsChange
}: UseSettingsOptions<T>): UseSettingsReturn<T> => {
  const [settings, setSettings] = useState<T>(() => {
    try {
      const savedSettings = localStorage.getItem(storageKey);
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        return {
          ...defaultSettings,
          ...parsedSettings
        };
      }
      return defaultSettings;
    } catch (error) {
      console.error(`Error loading settings from ${storageKey}:`, error);
      return defaultSettings;
    }
  });

  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(storageKey, JSON.stringify(settings));
      } catch (error) {
        console.error(`Error saving settings to ${storageKey}:`, error);
      }
    }
  }, [settings, storageKey, isLoaded]);

  useEffect(() => {
    if (onSettingsChange && isLoaded) {
      onSettingsChange(settings);
    }
  }, [settings, onSettingsChange, isLoaded]);

  const updateSettings = useCallback((newSettings: Partial<T>) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      ...newSettings
    }));
  }, []);

  const resetToDefaults = useCallback(() => {
    setSettings(defaultSettings);
  }, [defaultSettings]);

  return {
    settings,
    updateSettings,
    resetToDefaults,
    isLoaded
  };
};

export default useSettings;
