import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context';
import { PageMetadata } from '../../hoc/withPageMetadata';
import { PERMISSIONS, AUTH_CONFIG } from '../../config/authConfig';
import { useResponsive } from '../../hooks';
import { importedAssets } from '../../config/assetConfig';

const Register: React.FC & { pageMetadata: PageMetadata } = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/main/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!firstName.trim() || !lastName.trim() || !email.trim() || !password.trim()) {
      setError('Por favor, completa todos los campos');
      return;
    }

    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden');
      return;
    }

    if (!acceptTerms) {
      setError('Debes aceptar los términos y condiciones');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      navigate('/auth/login', {
        state: {
          message: 'Cuenta creada exitosamente. Por favor, inicia sesión.'
        }
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Error al crear la cuenta');
    } finally {
      setIsLoading(false);
    }
  };
  const { isMobile, isTablet } = useResponsive();
  const showBackground = !isMobile() && !isTablet();

  return (
    <div className="position-relative overflow-hidden radial-gradient min-vh-100 w-100">
      <div className="position-relative z-index-5">
        <div className="row">
          <div className="col-xl-5 col-xxl-4">
            <div className="authentication-login min-vh-100 bg-body row justify-content-center">
              <div className="col-12 text-center">
                <a href={AUTH_CONFIG.homePath} className="text-nowrap logo-img d-flex align-items-center justify-content-center gap-2 px-4 py-9 w-100">
                  <span className="logo-icon">
                    <img src={importedAssets.rayco.swooshDark} alt="logo" className="dark-logo" width="100" />
                  </span>
                  <span className="logo-icon">
                    <img src={importedAssets.rayco.mercosurLogo} alt="logo" className="dark-logo ps-2" width="100" />
                  </span>
                  <span className="logo-icon">
                    <img src={importedAssets.rayco.adidasBarDark} alt="logo" className="dark-logo ps-2" width="100" />
                  </span>
                </a>
              </div>
              <div className="auth-max-width col-sm-8 col-md-6 col-xl-7 px-4">
                <h2 className="mb-1 fs-7 fw-bolder">Crear Cuenta</h2>
                <p className="mb-7">Regístrate para acceder al panel de administración</p>

                {/* Formulario de registro */}
                <form onSubmit={handleSubmit}>
                  {/* Mensaje de error */}
                  {error && (
                    <div className="alert alert-danger" role="alert">
                      {error}
                    </div>
                  )}

                  {/* Campos de nombre y apellido */}
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="firstName" className="form-label">Nombre</label>
                      <input
                        type="text"
                        className="form-control"
                        id="firstName"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        disabled={isLoading}
                      />
                    </div>
                    <div className="col-md-6 mb-3">
                      <label htmlFor="lastName" className="form-label">Apellido</label>
                      <input
                        type="text"
                        className="form-control"
                        id="lastName"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  {/* Campo de email */}
                  <div className="mb-3">
                    <label htmlFor="email" className="form-label">Correo electrónico</label>
                    <input
                      type="email"
                      className="form-control"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled={isLoading}
                    />
                  </div>

                  {/* Campos de contraseña */}
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="password" className="form-label">Contraseña</label>
                      <input
                        type="password"
                        className="form-control"
                        id="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        disabled={isLoading}
                      />
                    </div>
                    <div className="col-md-6 mb-3">
                      <label htmlFor="confirmPassword" className="form-label">Confirmar contraseña</label>
                      <input
                        type="password"
                        className="form-control"
                        id="confirmPassword"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  {/* Términos y condiciones */}
                  <div className="mb-4">
                    <div className="form-check">
                      <input
                        className="form-check-input primary"
                        type="checkbox"
                        id="acceptTerms"
                        checked={acceptTerms}
                        onChange={(e) => setAcceptTerms(e.target.checked)}
                        disabled={isLoading}
                      />
                      <label className="form-check-label text-dark fs-3" htmlFor="acceptTerms">
                        Acepto los <a href="#" className="text-primary">términos y condiciones</a>
                      </label>
                    </div>
                  </div>

                  {/* Botón de registro */}
                  <button
                    type="submit"
                    className="btn btn-primary w-100 py-8 mb-4"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Creando cuenta...
                      </>
                    ) : 'Crear Cuenta'}
                  </button>

                  {/* Enlace de login */}
                  <div className="d-flex align-items-center justify-content-center">
                    <p className="fs-4 mb-0 fw-medium">¿Ya tienes una cuenta?</p>
                    <a className="text-primary fw-medium ms-2" href="/auth/login">
                      Iniciar sesión
                    </a>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {/* Imagen de fondo (solo en pantallas grandes) */}
          {showBackground && (
            <div className="col-xl-7 col-xxl-8">
              <div className="d-flex align-items-center justify-content-center h-100" style={{backgroundColor: '#e5ecf9'}}>
                <img src={importedAssets.backgrounds.login} alt="Auth background" className="img-fluid" />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Define page metadata
Register.pageMetadata = {
  title: 'Registro',
  description: 'Crear una nueva cuenta',
  requiresAuth: false,
  path: '/auth/register',
  showInMenu: false,
  permissions: {
    requiredPermission: PERMISSIONS.NONE,
    resourceId: 'register'
  }
};

export default Register;
