import React from 'react';
import withLoading from './withLoading';
import withError from './withError';

interface WithDataFetchingProps<T> {
  data: T | null;
  loading: boolean;
  error: Error | string | null;
  onRetry?: () => void;
  loadingMessage?: string;
  emptyMessage?: string;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
}


export function withDataFetching<T, P extends { data: T }>(
  Component: React.ComponentType<P>
): React.FC<Omit<P, 'data'> & WithDataFetchingProps<T>> {
  const WithErrorComponent = withError<{ data: T | null } & Omit<P, 'data'>>(
    Component as React.ComponentType<{ data: T | null } & Omit<P, 'data'>>
  );

  const WithLoadingAndErrorComponent = withLoading<Omit<P, 'data'> & WithErrorProps<T>>(WithErrorComponent);

  return function WithDataFetchingComponent({
    data,
    loading,
    error,
    onRetry,
    loadingMessage,
    emptyMessage = 'No data available',
    loadingComponent,
    errorComponent,
    emptyComponent,
    ...props
  }: WithDataFetchingProps<T> & Omit<P, 'data'>) {
    if (!loading && !error && (!data || (Array.isArray(data) && data.length === 0))) {
      if (emptyComponent) {
        return <>{emptyComponent}</>;
      }
      
      return (
        <div className="alert alert-info" role="alert">
          <div className="d-flex align-items-center">
            <div className="me-3">
              <i className="ti ti-info-circle fs-4"></i>
            </div>
            <div>
              <p className="mb-0">{emptyMessage}</p>
            </div>
          </div>
        </div>
      );
    }
    
    return (
      <WithLoadingAndErrorComponent
        isLoading={loading}
        loadingMessage={loadingMessage}
        loadingComponent={loadingComponent}
        error={error}
        onRetry={onRetry}
        errorComponent={errorComponent}
        data={data}
        {...(props as unknown) as Omit<P, 'data'>}
      />
    );
  };
}

interface WithErrorProps<T> {
  error: Error | string | null;
  onRetry?: () => void;
  errorComponent?: React.ReactNode;
  data: T | null;
}

export default withDataFetching;
