
import React, { forwardRef } from 'react';
import { Form } from 'react-bootstrap';

export interface FormTextareaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'onChange'> {
  id?: string;
  name: string;
  label?: string;
  helpText?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  readOnly?: boolean;
  rows?: number;
  showCharCount?: boolean;
  maxLength?: number;
  onChange?: (value: string) => void;
  onFocus?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
}

const FormTextarea = forwardRef<HTMLTextAreaElement, FormTextareaProps>(
  (
    {
      id,
      name,
      label,
      helpText,
      error,
      required = false,
      disabled = false,
      readOnly = false,
      rows = 3,
      showCharCount = false,
      maxLength,
      value = '',
      placeholder,
      onChange,
      onFocus,
      onBlur,
      ...rest
    },
    ref
  ) => {
    const textareaId = id || `form-textarea-${name}`;
    const currentLength = typeof value === 'string' ? value.length : 0;
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      if (onChange) {
        onChange(e.target.value);
      }
    };

    return (
      <div className="mb-3">
        {label && (
          <label
            htmlFor={textareaId}
            className={`form-label${required ? ' required' : ''}`}
          >
            {label}
          </label>
        )}

        <Form.Control
          ref={ref}
          as="textarea"
          id={textareaId}
          name={name}
          rows={rows}
          placeholder={placeholder}
          disabled={disabled}
          readOnly={readOnly}
          required={required}
          value={typeof value === 'string' ? value : ''}
          onChange={handleChange}
          onFocus={onFocus}
          onBlur={onBlur}
          maxLength={maxLength}
          isInvalid={!!error}
          {...rest}
        />

        {showCharCount && maxLength && (
          <div className="d-flex justify-content-end mt-1">
            <small className={`text-muted${currentLength > maxLength ? ' text-danger' : ''}`}>
              {currentLength}/{maxLength}
            </small>
          </div>
        )}

        {error && (
          <div className="invalid-feedback d-block">
            {error}
          </div>
        )}

        {helpText && (
          <div className="form-text text-muted">
            {helpText}
          </div>
        )}
      </div>
    );
  }
);

FormTextarea.displayName = 'FormTextarea';

export default FormTextarea;
