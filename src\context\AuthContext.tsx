

import React, { useState, ReactNode, useEffect, useRef, createContext, useContext } from 'react';
import {
  PermissionLevel,
  AUTH_STORAGE_KEY,
  AUTH_CONFIG,
  authUtils,
  PERMISSIONS
} from '../config/authConfig';

import { API_ROUTES, apiUtils, apiEndpoints } from '../config/apiConfig';
import { preloadValidRoutes } from '../utils/preloadValidRoutes';
import { fetchDatosPersonalesBL } from '../services/datosPersonalesService';
import { callProcedure } from '../api/procedureService';

export interface DatosPersonasBL {
  IdPersona: number;
  Codigo: string;
  Nombre: string;
  DomicilioCalle: string;
  DomicilioLoc: string;
  DocNro: string;
  Sexo: string;
  FecNacimiento: string;
  Telefono: string;
  Foto?: string | null;
  CUIL: string;
  EmailPersonal: string;
  FecIngreso?: string;
  LimiteCredito?: string;
  Estado?: 'activo' | 'inactivo';
}

export interface UserProfile {
  id: string;
  email: string;
  avatar?: string;
  role: string;

  nombres?: string;
  apellidos?: string;
  alias?: string;
  correo?: string;
  primera_vez?: boolean;
  fecha_baja?: string;
  fecha_prox_cambio_clave?: string;
  fecha_registro?: string;
  estado?: number;
  id_ctacte?: number;
  id_rol?: number;
  nrodoc?: string;
  nrolegajo?: string;
  id_empresa?: number;
  id_sucursal?: number;

  datosBLaboro?: DatosPersonasBL;
}

export interface AuthUser extends UserProfile {
  permissions: {
    [resourceId: string]: PermissionLevel;
  };
}

export interface AuthContextType {
  isAuthenticated: boolean;
  user: AuthUser | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  updateUserProfile: (profile: Partial<UserProfile>) => void;
  updateDatosPersonales: (datosBLaboro: DatosPersonasBL | null) => void;
  hasPermission: (resourceId: string, requiredPermission: PermissionLevel) => boolean;
  clearPermissionCache: () => void;
  getFullName: () => string;
  getInitials: () => string;
}

const defaultAuthContext: AuthContextType = {
  user: null,
  isAuthenticated: false,
  login: async () => false,
  logout: () => {},
  updateUserProfile: () => {},
  updateDatosPersonales: () => {},
  hasPermission: () => false,
  clearPermissionCache: () => {},
  getFullName: () => '',
  getInitials: () => ''
};

export const AuthContext = createContext<AuthContextType>(defaultAuthContext);

export const useAuth = () => useContext(AuthContext);


interface BackendRole {
  id_rol: number;
  nombre: string;
  descripcion: string;
}

export interface ColaboradorApiResponse {
  mensaje: string;
  success: boolean;
  data: {
    LimiteCredito: string;
    CodEmpresa: number;
    Legajo: string;
    Nombre: string;
    DomicilioCalle: string;
    FecNacimiento: string;
    Telefono: string;
    FecIngreso: string;
    Email: string;
    EstadoConsulta: string;
  }[];
}

export interface ColaboradorAttributesResponse {
  mensaje: string;
  success: boolean;
  data: {
    CUEPREFI: string;
    CODCTACTE: string;
    CODATR: string;
    DescripcionAtributo: string;
    CODATRVAL: string;
    ValorAtributo: string;
  }[];
}

export async function fetchColaboradorData(codCtaCte: string, nroDoc: string): Promise<ColaboradorApiResponse> {
  try {
    if (!codCtaCte) {
      console.error('Missing required parameter: codCtaCte');
      throw new Error('Missing required parameter: codCtaCte');
    }

    if (!nroDoc) {
      console.error('Missing required parameter: nroDoc');
      throw new Error('Missing required parameter: nroDoc');
    }

    return await callProcedure<ColaboradorApiResponse>('SP_GET_INFO_COLAB_APP', {
      '@CodCtaCte': codCtaCte,
      '@NroDoc': nroDoc
    }, 'sql');
  } catch (error) {
    console.error('Error fetching colaborador data:', error);
    throw error;
  }
}

export async function fetchColaboradorAttributes(codCtaCte: string): Promise<ColaboradorAttributesResponse> {
  try {
    if (!codCtaCte) {
      console.error('Missing required parameter: codCtaCte');
      throw new Error('Missing required parameter: codCtaCte');
    }
    return await callProcedure<ColaboradorAttributesResponse>('SP_GET_ATRIB_COLAB_APP', {
      '@CodCtaCte': codCtaCte
    }, 'sql');
  } catch (error) {
    console.error('Error fetching colaborador attributes:', error);
    throw error;
  }
}

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(() => {
    try {
      const savedUser = localStorage.getItem(AUTH_STORAGE_KEY);
      return savedUser ? JSON.parse(savedUser) : null;
    } catch (error) {
      console.error('Error loading user data:', error);
      return null;
    }
  });
  const [loginAttempts, setLoginAttempts] = useState<Record<string, number>>({});
  const [blockedUsers, setBlockedUsers] = useState<Record<string, number>>({});
  const [, setModuleToResourceMap] = useState<Record<string, string>>({});
  const [, setCodeToResourceMap] = useState<Record<string, string>>({});
  const fetchResourceMappings = async (): Promise<{
    moduleMap: Record<string, string>;
    codeMap: Record<string, string>;
  }> => {
    try {
      const url = apiEndpoints.pg.handle;
      const requestData = {
        call: "fn_obtener_modulos_y_codigos",
        type: "func",
        parametros: {}
      };
      const response = await apiUtils.post(url, requestData, {
        auth: true
      });
      const data = response;
      if (!data.success || !data.datos || data.datos.length === 0) {
        console.error('[AUTH] Error getting resource mappings:', data.mensaje || 'No mappings found');

        const defaultModuleMap: Record<string, string> = {
          'Dashboard': 'dashboard'
        };
        const defaultCodeMap: Record<string, string> = {
          'dashboard_view': 'dashboard'
        };
        setModuleToResourceMap(defaultModuleMap);
        setCodeToResourceMap(defaultCodeMap);
        return { moduleMap: defaultModuleMap, codeMap: defaultCodeMap };
      }
      const moduleMap: Record<string, string> = {};
      const codeMap: Record<string, string> = {};
      data.datos.forEach((item: { modulo: string; codigo: string }) => {
        const resourceId = item.modulo.toLowerCase();
        moduleMap[item.modulo] = resourceId;
        codeMap[item.codigo] = resourceId;
      });
      setModuleToResourceMap(moduleMap);
      setCodeToResourceMap(codeMap);
      return { moduleMap, codeMap };
    } catch (error) {
      console.error('[AUTH] Error fetching resource mappings:', error);
      const defaultModuleMap: Record<string, string> = {
        'Dashboard': 'dashboard',
        'Miperfil': 'miperfil',
        'Estadodecuenta': 'estadodecuenta'
      };
      const defaultCodeMap: Record<string, string> = {
        'dashboard_view': 'dashboard',
        'dashboard_edit': 'dashboard',
        'dashboard_full': 'dashboard',
        'miperfil_view': 'miperfil',
        'miperfil_edit': 'miperfil',
        'miperfil_full': 'miperfil',
        'estadodecuenta_view': 'estadodecuenta',
        'estadodecuenta_edit': 'estadodecuenta',
        'estadodecuenta_full': 'estadodecuenta'
      };
      setModuleToResourceMap(defaultModuleMap);
      setCodeToResourceMap(defaultCodeMap);
      return { moduleMap: defaultModuleMap, codeMap: defaultCodeMap };
    }
  };

  const isAuthenticated = user !== null;
  useEffect(() => {
    if (user) {
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(user));
    } else {
      localStorage.removeItem(AUTH_STORAGE_KEY);
    }
    permissionCacheRef.current.clear();
    if (process.env.NODE_ENV === 'development') {}
  }, [user]);

  useEffect(() => {
    if (isAuthenticated) {
      preloadValidRoutes();
    }
  }, []);

  const fetchUserRole = async (userId: string): Promise<string> => {
    try {
      const url = apiEndpoints.pg.handle;
      const requestData = {
        call: "fn_obtener_rol_usuario",
        type: "func",
        parametros: {
          p_id_usuario: parseInt(userId)
        }
      };
      const response = await apiUtils.post(url, requestData, {
        auth: true
      });

      const data = response;
      if (!data.success || !data.datos || data.datos.length === 0) {
        console.error('[AUTH] Error getting user role:', data.mensaje || 'Role not found');
        return 'user'; // Rol por defecto
      }

      const roleData = data.datos[0] as BackendRole;
      const roleMap: Record<string, string> = {
        'Administrador': 'admin',
        'Gerente': 'manager',
        'Usuario': 'user',
        'Invitado': 'guest'
      };
      const role = roleMap[roleData.nombre] || roleData.nombre.toLowerCase();


      return role;
    } catch (error) {
      console.error('[AUTH] Error fetching user role:', error);
      return 'user'; // Rol por defecto en caso de error
    }
  };


  const fetchUserPermissions = async (userId: string): Promise<Record<string, PermissionLevel>> => {
    try {
      const url = apiEndpoints.pg.handle;
      const requestData = {
        call: "fn_get_all_permisos_usuario",
        type: "func",
        parametros: {
          p_id_usuario: parseInt(userId)
        }
      };

      const response = await apiUtils.post(url, requestData, {
        auth: true
      });

      const data = response;
      if (!data.success) {
        console.error('Error al obtener permisos:', data.mensaje);
        return {
          'dashboard': PERMISSIONS.VIEW,
          'miperfil': PERMISSIONS.VIEW,
          'estadodecuenta': PERMISSIONS.VIEW
        };
      }

      const permissions: Record<string, PermissionLevel> = {};
      const backendPermissions = data.datos as {
        id_modulo: number;
        codigo_modulo: string;
        nombre_modulo: string;
        permission_mask: number;
      }[];

      const allResourceIds = new Set<string>();
      backendPermissions.forEach(permission => {

        const resourceId = permission.codigo_modulo.toLowerCase();
        allResourceIds.add(resourceId);
        permissions[resourceId] = permission.permission_mask;

      });

      const coreResources = ['dashboard', 'miperfil', 'estadodecuenta'];
      coreResources.forEach(resourceId => {
        if (!permissions[resourceId]) {
          permissions[resourceId] = PERMISSIONS.VIEW;
        }
      });

      return permissions;
    } catch (error) {
      console.error('[AUTH] Error fetching user permissions:', error);

      const defaultPermissions = {
        'dashboard': PERMISSIONS.VIEW,
        'miperfil': PERMISSIONS.VIEW,
        'estadodecuenta': PERMISSIONS.VIEW,
      };

      return defaultPermissions;
    }
  };


  const login = async (nrodoc: string, password: string): Promise<boolean> => {

    const now = Date.now();
    const blockedUntil = blockedUsers[nrodoc];

    if (blockedUntil && blockedUntil > now) {
      const remainingMinutes = Math.ceil((blockedUntil - now) / 60000);
      throw new Error(`Usuario bloqueado. Intente nuevamente en ${remainingMinutes} minutos.`);
    }

    try {
      const loginUrl = apiEndpoints.pg.base + API_ROUTES.auth.login;
      const data = await apiUtils.post(loginUrl, { nrodoc: nrodoc, password });

      if (data.success) {

        setLoginAttempts(prev => ({
          ...prev,
          [nrodoc]: 0
        }));


        setBlockedUsers(prev => {
          const newBlocked = { ...prev };
          delete newBlocked[nrodoc];
          return newBlocked;
        });


        const token = data.token;
        localStorage.setItem(AUTH_CONFIG.tokenKey, token);
        const userData = data.datos;
        const userId = userData.id_usuario.toString();
        await fetchResourceMappings();
        const userPermissions = await fetchUserPermissions(userId);
        const userRole = await fetchUserRole(userId);

        let datosBLaboro = null;
        try {
          if (userData.nrodoc) {
            console.log(`[AUTH] Fetching personal data for document: ${userData.nrodoc}`);
            datosBLaboro = await fetchDatosPersonalesBL(userData.nrodoc);
            if (datosBLaboro) {
              console.log(`[AUTH] Successfully retrieved personal data for: ${datosBLaboro.Nombre}`);
              try {
                const codCtaCte = userData.id_ctacte?.toString();
                if (codCtaCte) {
                  console.log(`[AUTH] Fetching colaborador data for account: ${codCtaCte}`);
                  const colaboradorResponse = await fetchColaboradorData(codCtaCte, userData.nrodoc);

                  if (colaboradorResponse.success && colaboradorResponse.data && colaboradorResponse.data.length > 0) {
                    const colaboradorData = colaboradorResponse.data[0];
                    let estado: 'activo' | 'inactivo' = 'activo';
                    try {
                      const attributesResponse = await fetchColaboradorAttributes(codCtaCte);
                      if (attributesResponse.success && attributesResponse.data) {
                        const statusAttribute = attributesResponse.data.find(attr => attr.CODATR === 'C05');
                        if (statusAttribute) {
                          estado = statusAttribute.CODATRVAL === '2' ? 'activo' : 'inactivo';
                        }
                      }
                    } catch (attributesError) {
                      console.warn('[AUTH] Error fetching colaborador attributes:', attributesError);
                    }


                    datosBLaboro = {
                      ...datosBLaboro,
                      FecIngreso: colaboradorData.FecIngreso,
                      LimiteCredito: colaboradorData.LimiteCredito,
                      Estado: estado
                    };

                    console.log(`[AUTH] Successfully merged colaborador data`);
                  }
                }
              } catch (colaboradorError) {
                console.warn('[AUTH] Error fetching colaborador data during login:', colaboradorError);
              }
            } else {
              console.warn(`[AUTH] No personal data found for document: ${userData.nrodoc}`);
            }
          }
        } catch (error) {
          console.error('[AUTH] Error fetching personal data during login:', error);
        }

        const newUser = {
          id: userId,
          email: userData.correo,
          nrodoc: userData.nrodoc,
          role: userRole,
          permissions: userPermissions,
          nombres: userData.nombres,
          apellidos: userData.apellidos,
          alias: userData.alias,
          primera_vez: userData.primera_vez,
          fecha_baja: userData.fecha_baja,
          fecha_prox_cambio_clave: userData.fecha_prox_cambio_clave,
          fecha_registro: userData.fecha_registro,
          estado: userData.estado,
          id_ctacte: userData.id_ctacte,
          id_rol: userData.id_rol,
          nrolegajo: userData.nrolegajo,
          id_empresa: userData.id_empresa,
          id_sucursal: userData.id_sucursal,
          datosBLaboro: datosBLaboro || undefined
        };
        setUser(newUser);
        preloadValidRoutes();
        return true;
      }

      const currentAttempts = (loginAttempts[nrodoc] || 0) + 1;
      setLoginAttempts(prev => ({
        ...prev,
        [nrodoc]: currentAttempts
      }));

      if (currentAttempts >= AUTH_CONFIG.maxLoginAttempts) {
        const blockUntil = now + (AUTH_CONFIG.lockoutDuration * 60000); // Convertir minutos a milisegundos
        setBlockedUsers(prev => ({
          ...prev,
          [nrodoc]: blockUntil
        }));

        throw new Error(`Demasiados intentos fallidos. Usuario bloqueado por ${AUTH_CONFIG.lockoutDuration} minutos.`);
      }

      return false;
    } catch (error) {
      if (error instanceof Error && error.message.includes('bloqueado')) {
        throw error; // Re-lanzar errores de bloqueo
      }
      console.error('Error during login:', error);
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem(AUTH_CONFIG.tokenKey);
    setUser(null);
  };

  const updateUserProfile = (profile: Partial<UserProfile>) => {
    if (user) {
      setUser({ ...user, ...profile });
    }
  };

  const updateDatosPersonales = (datosBLaboro: DatosPersonasBL | null) => {
    if (user) {
      const updatedUser = { ...user, datosBLaboro: datosBLaboro || undefined };
      setUser(updatedUser);
      localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(updatedUser));
    }
  };

  const permissionCacheRef = useRef<Map<string, boolean>>(new Map());
  const hasPermission = (resourceId: string, requiredPermission: PermissionLevel): boolean => {
    const cacheKey = `${resourceId}:${requiredPermission}`;

    if (permissionCacheRef.current.has(cacheKey)) {
      const cachedResult = permissionCacheRef.current.get(cacheKey)!;
      return cachedResult;
    }
    if (!user) {
      permissionCacheRef.current.set(cacheKey, false);
      return false;
    }
    const userPermission = user.permissions[resourceId] || PERMISSIONS.NONE;
    const result = authUtils.hasPermission(userPermission, requiredPermission);
    permissionCacheRef.current.set(cacheKey, result);

    return result;
  };

  const getFullName = (): string => {
    if (!user) return '';
    if (user.datosBLaboro?.Nombre) {
      return user.datosBLaboro.Nombre.trim();
    }
    return user.nombres && user.apellidos ? `${user.nombres} ${user.apellidos}`.trim() : '';
  };

  const getInitials = (): string => {
    if (!user) return '';
    if (user.datosBLaboro?.Nombre) {
      const nameParts = user.datosBLaboro.Nombre.split(' ');
      const firstInitial = nameParts[0]?.charAt(0) || '';
      const lastInitial = nameParts[nameParts.length - 1]?.charAt(0) || '';
      return `${firstInitial}${lastInitial}`.toUpperCase();
    }
    if (user.nombres && user.apellidos) {
      return `${user.nombres.charAt(0)}${user.apellidos.charAt(0)}`.toUpperCase();
    }
    return '';
  };

  const clearPermissionCache = (): void => {
    permissionCacheRef.current.clear();
  };

  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    login,
    logout,
    updateUserProfile,
    updateDatosPersonales,
    hasPermission,
    clearPermissionCache,
    getFullName,
    getInitials
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
