import React from 'react';
import { HashRouter as Router } from 'react-router-dom';
import {
  ThemeProvider,
  LayoutProvider,
  Auth<PERSON>rovider,
  PageProvider,
  ApiProvider,
  AnimationProvider
} from './';

interface AppContextProviderProps {
  children: React.ReactNode;
}


export const AppContextProvider: React.FC<AppContextProviderProps> = ({ children }) => {
  return (
    <ThemeProvider>
      <LayoutProvider>
        <AnimationProvider>
          <ApiProvider>
            <AuthProvider>
              <Router>
                <PageProvider>
                  {children}
                </PageProvider>
              </Router>
            </AuthProvider>
          </ApiProvider>
        </AnimationProvider>
      </LayoutProvider>
    </ThemeProvider>
  );
};

export default AppContextProvider;