import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { PageMetadata } from '@/hoc/withPageMetadata';
import maintenanceImg from '@/assets/images/backgrounds/maintenance.svg';
import { AUTH_CONFIG } from '@/config/authConfig';
import { PERMISSIONS } from '@/config/authConfig';


const Maintenance: React.FC & { pageMetadata: PageMetadata } = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [countdown, setCountdown] = useState(5);
  const getPreviousPage = useCallback(() => {
    return location.state?.from?.pathname || AUTH_CONFIG.homePath;
  }, [location.state]);

  const handleRedirect = useCallback(() => {
    const previousPage = getPreviousPage();
    if (previousPage === location.pathname || previousPage.includes('/maintenance')) {
      navigate(AUTH_CONFIG.homePath, { replace: true });
    } else {
      navigate(previousPage, { replace: true });
    }
  }, [navigate, getPreviousPage, location.pathname]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (countdown <= 1) {
        handleRedirect();
      } else {
        setCountdown(countdown - 1);
      }
    }, 1000);
    return () => {
      clearTimeout(timer);
    };
  }, [handleRedirect, countdown]);

  return (
      <div className="position-relative overflow-hidden min-vh-100 w-100 d-flex align-items-center justify-content-center">
        <div className="row justify-content-center w-100">
          <div className="col-lg-4">
            <div className="text-center">
              <img src={maintenanceImg} alt="Mantenimiento" className="img-fluid" width="500" />
              <h1 className="fw-semibold my-7 fs-9">Modo Mantenimiento</h1>
              <h4 className="fw-semibold mb-7">No tienes permisos para acceder a esta página.</h4>
              <p className="mb-3">Serás redirigido en <span className="fw-bold text-primary">{countdown}</span> segundos...</p>
              <button
                  className="btn btn-primary"
                  onClick={handleRedirect}
              >
                Volver ahora
              </button>
            </div>
          </div>
        </div>
      </div>
  );
};

// Define page metadata
Maintenance.pageMetadata = {
  title: 'Mantenimiento',
  description: 'Página en mantenimiento',
  requiresAuth: false,
  path: '/auth/maintenance',
  showInMenu: false,
  permissions: {
    requiredPermission: PERMISSIONS.NONE,
    resourceId: 'maintenance'
  }
};

export default Maintenance;
