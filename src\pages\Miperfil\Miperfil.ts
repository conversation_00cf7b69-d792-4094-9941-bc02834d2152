export function formatDate(dateStr: string): string {
  if (!dateStr) return '';
  const [year, month, day] = dateStr.split('-').map((num: string) => parseInt(num, 10));
  return `${day}/${month}/${year}`;
}

export function calculateSeniority(contractDate: string): string {
  if (!contractDate) return '';
  const today = new Date();
  let contract: Date;

  if (contractDate.includes('-')) {
    const [year, month, day] = contractDate.split('-').map((num: string) => parseInt(num, 10));
    contract = new Date(year, month - 1, day);
  } else {
    contract = new Date(contractDate);
  }

  if (contract > today) {
    return "1 mes";
  }

  let years = today.getFullYear() - contract.getFullYear();
  let months = today.getMonth() - contract.getMonth();

  if (today.getDate() < contract.getDate()) {
    months--;
  }

  if (months < 0) {
    years--;
    months += 12;
  }

  if (years === 0 && months === 0) {
    return "1 mes";
  }

  if (years === 0) {
    return months === 1 ? "1 mes" : `${months} meses`;
  } else if (months === 0) {
    return years === 1 ? "1 año" : `${years} años`;
  } else {
    const yearText = years === 1 ? "1 año" : `${years} años`;
    const monthText = months === 1 ? "1 mes" : `${months} meses`;
    return `${yearText}, ${monthText}`;
  }
}

