
import { useState, useEffect, useCallback, useRef } from 'react';
import { get } from './http';

export interface UseApiOptions<T = never> {
  initialFetch?: boolean;
  params?: Record<string, unknown>;
  dependencies?: never[];
  mockData?: T;
  useMockInProduction?: boolean;
}

export interface UseApiResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  fetch: (overrideParams?: Record<string, unknown>) => Promise<void>;
  reset: () => void;
}

export function useApi<T>(
  endpoint: string,
  options: UseApiOptions<T> = {}
): UseApiResult<T> {
  const { 
    initialFetch = true, 
    params, 
    dependencies = [],
    mockData = null,
    useMockInProduction = false
  } = options;
  
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(initialFetch);
  const [error, setError] = useState<Error | null>(null);
  const isMounted = useRef(true);
  
  const fetch = useCallback(async (overrideParams?: Record<string, unknown>) => {
    if (!isMounted.current) return;
    try {
      setLoading(true);
      setError(null);
      
      const queryParams = overrideParams || params;
      const response = await get<T>(endpoint, queryParams);
      if (response.error) {
        throw new Error(response.error);
      }
      if (isMounted.current) {
        setData(response.data);
        setError(null);
      }
    } catch (err) {
      if (isMounted.current) {
        setError(err instanceof Error ? err : new Error('An unknown error occurred'));
        setData(null);
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
      }
    }
  }, [endpoint, params, mockData, useMockInProduction]);
  
  const reset = useCallback(() => {
    setData(null);
    setLoading(false);
    setError(null);
  }, []);
  
  useEffect(() => {
    if (initialFetch) {
      fetch();
    }
    return () => {
      isMounted.current = false;
    };
  }, [initialFetch, fetch, ...dependencies]);
  return { data, loading, error, fetch, reset };
}

export default useApi;
