import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context';
import { PageMetadata } from '@/hoc/withPageMetadata';
import { AUTH_CONFIG, PERMISSIONS } from '@/config/authConfig';
import { useResponsive } from '@/hooks';
import { importedAssets } from '@/config';
import { DIMENSIONS } from '@/config/responsiveConfig';

const Login: React.FC & { pageMetadata: PageMetadata } = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isAuthenticated } = useAuth();
  const [nrodoc, setNrodoc] = useState('');
  const [password, setPassword] = useState('');
  const [rememberDevice, setRememberDevice] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loginSuccess, setLoginSuccess] = useState(false);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated && loginSuccess && redirectPath) {
      navigate(redirectPath, { replace: true });
    }
  }, [isAuthenticated, loginSuccess, redirectPath, navigate]);

  useEffect(() => {
    if (isAuthenticated && !loginSuccess) {
      navigate(AUTH_CONFIG.homePath, { replace: true });
    }
  }, [isAuthenticated, loginSuccess, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!nrodoc.trim() || !password.trim()) {
      setError('Por favor, ingresa tu número de documento y contraseña');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const success = await login(nrodoc, password);

      if (success) {
        const from = location.state?.from?.pathname || AUTH_CONFIG.homePath;
        setLoginSuccess(true);
        setRedirectPath(from);
        setTimeout(() => {
          navigate(from, { replace: true });
        }, 500);
      } else {
        setError('Credenciales inválidas. Por favor, intenta nuevamente.');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Error al iniciar sesión');
    } finally {
      setIsLoading(false);
    }
  };

  const { isMobile, isTablet } = useResponsive();
  const isMobileValue = isMobile();
  const isTabletValue = isTablet();
  const showBackground = !isMobileValue && !isTabletValue;

  return (
    <div className="position-relative overflow-hidden radial-gradient min-vh-100 w-100">
      <div className="position-relative z-index-5">
        <div className="row">
          <div className="col-xl-5 col-xxl-4">
            <div className="authentication-login min-vh-100 bg-body row justify-content-center">
              <div className="col-12 text-center">
                {/* Company logos - visible on mobile */}
                {isMobileValue && (
                  <a href={AUTH_CONFIG.homePath} className="text-nowrap logo-img d-flex align-items-center justify-content-center gap-3 px-4 py-6 w-100">
                    <span className="logo-icon" style={{zIndex: DIMENSIONS.zIndex.fullscreen, position: 'relative'}}>
                      <img src={importedAssets.rayco.swooshDark} alt="logo" width="120"/>
                    </span>
                    <span className="logo-icon" style={{zIndex: DIMENSIONS.zIndex.fullscreen, position: 'relative'}}>
                      <img src={importedAssets.rayco.mercosurLogo} alt="logo" width="120"/>
                    </span>
                    <span className="logo-icon" style={{zIndex: DIMENSIONS.zIndex.fullscreen, position: 'relative'}}>
                      <img src={importedAssets.rayco.swooshBNikeNegro} alt="logo" width="120"/>
                    </span>
                  </a>
                )}

                {/* Main logo - hidden on mobile */}
                {!isMobileValue && (
                  <a href={AUTH_CONFIG.homePath} className="text-nowrap logo-img d-flex align-items-center justify-content-center gap-3 px-4 py-9 w-100" style={{zIndex: DIMENSIONS.zIndex.fullscreen, position: 'relative'}}>
                    <span className="logo-icon" style={{zIndex: DIMENSIONS.zIndex.fullscreen, position: 'relative'}}>
                      <img src={importedAssets.rayco.swooshDark} alt="logo" width="120"/>
                    </span>
                    <span className="logo-icon" style={{zIndex: DIMENSIONS.zIndex.fullscreen, position: 'relative'}}>
                      <img src={importedAssets.rayco.mercosurLogo} alt="logo" width="120"/>
                    </span>
                    <span className="logo-icon" style={{zIndex: DIMENSIONS.zIndex.fullscreen, position: 'relative'}}>
                      <img src={importedAssets.rayco.swooshBNikeNegro} alt="logo" width="120"/>
                    </span>
                  </a>
                )}
              </div>
              <div className="auth-max-width col-sm-8 col-md-6 col-xl-7 px-4">
                <h2 className="mb-1 fs-6 fw-bolder">Bienvenido a tu portal</h2>
                <p className="mb-4"></p>

                {/* Formulario de login */}
                <form onSubmit={handleSubmit}>
                  {/* Mensaje de error */}
                  {error && (
                    <div className="alert alert-danger" role="alert">
                      {error}
                    </div>
                  )}

                  {/* Campo de número de documento */}
                  <div className="mb-3">
                    <label htmlFor="nrodoc" className="form-label">Número de documento</label>
                    <input
                      type="text"
                      className="form-control"
                      id="nrodoc"
                      value={nrodoc}
                      onChange={(e) => setNrodoc(e.target.value)}
                      disabled={isLoading}
                      placeholder="C.I."
                    />
                  </div>

                  {/* Campo de contraseña */}
                  <div className="mb-4">
                    <label htmlFor="password" className="form-label">Contraseña</label>
                    <input
                      type="password"
                      className="form-control"
                      id="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      disabled={isLoading}
                      placeholder="Contraseña"
                    />
                  </div>

                  {/* Opciones adicionales */}
                  <div className="d-flex align-items-center justify-content-between mb-4">
                    <div className="form-check">
                      <input
                        className="form-check-input primary"
                        type="checkbox"
                        id="rememberDevice"
                        checked={rememberDevice}
                        onChange={(e) => setRememberDevice(e.target.checked)}
                        disabled={isLoading}
                      />
                      <label className="form-check-label text-dark fs-3" htmlFor="rememberDevice">
                        Recordar este dispositivo
                      </label>
                    </div>
                    {/*<a className="text-primary fw-medium fs-3" href={AUTH_CONFIG.forgotPasswordPath}>*/}
                    {/*  ¿Olvidaste tu contraseña?*/}
                    {/*</a>*/}
                  </div>

                  {/* Botón de inicio de sesión */}
                  <button
                    type="submit"
                    className="btn btn-primary w-100 py-8 mb-4"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Iniciando sesión...
                      </>
                    ) : 'Iniciar Sesión'}
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* Imagen de fondo (solo en pantallas grandes) */}
          {showBackground && (
            <div className="col-xl-7 col-xxl-8">
              <div className="d-flex align-items-center justify-content-center h-100" style={{backgroundColor: '#e5ecf9'}}>
                <img src={importedAssets.backgrounds.login} alt="Auth background" className="img-fluid" />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Define page metadata
Login.pageMetadata = {
  title: 'Iniciar Sesión',
  description: 'Iniciar sesión en la plataforma',
  requiresAuth: false,
  path: AUTH_CONFIG.loginPath,
  permissions: {
    requiredPermission: PERMISSIONS.NONE,
    resourceId: 'login'
  }
};

export default Login;
