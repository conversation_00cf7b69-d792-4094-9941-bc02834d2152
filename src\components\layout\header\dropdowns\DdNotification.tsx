import React from 'react';
import { Link } from 'react-router-dom';
import { FA6 } from '@/config/icons/iconUtils';

interface Notification {
  icon: React.ReactNode;
  title: string;
  time: string;
  description: string;
}

const DdNotification: React.FC = () => {
  const notifications: Notification[] = [
    {
      icon: <FA6.FaBell className="fs-6" />,
      title: 'New Message',
      time: '2 min ago',
      description: 'You have received a new message from <PERSON>'
    },
    {
      icon: <FA6.FaBell className="fs-6" />,
      title: 'New Task',
      time: '5 min ago',
      description: 'A new task has been assigned to you'
    },
    {
      icon: <FA6.FaBell className="fs-6" />,
      title: 'System Update',
      time: '10 min ago',
      description: 'System update completed successfully'
    }
  ];

  return (
    <>
      <div className="py-3 px-4 bg-secondary">
        <div className="mb-0 fs-6 fw-medium text-white">Notifications</div>
        <div className="mb-0 fs-2 fw-medium text-white">You have 3 new notifications</div>
      </div>
      <div className="notification-body" data-simplebar>
        {notifications.map((notification, index) => (
          <div key={index} className="p-3 d-flex align-items-center dropdown-item gap-3 border-bottom">
            <div className="icon-wrapper">
              {notification.icon}
            </div>
            <div>
              <h6 className="mb-1">{notification.title}</h6>
              <p className="mb-0 text-muted">{notification.description}</p>
              <small className="text-muted">{notification.time}</small>
            </div>
          </div>
        ))}
      </div>
      <div className="p-3">
        <Link 
          className="d-flex btn btn-secondary align-items-center justify-content-center gap-2" 
          to="/notifications"
        >
          <span>Check all Notifications</span>
          <FA6.FaArrowRight className="fs-6" />
        </Link>
      </div>
    </>
  );
};

export default DdNotification; 