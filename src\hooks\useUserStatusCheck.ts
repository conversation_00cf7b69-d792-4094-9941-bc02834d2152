import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/context';
import { fetchColaboradorAttributes, ColaboradorAttributesResponse } from '@/context/AuthContext';

export interface UserStatusResult {
  status: 'active' | 'inactive' | null;
  loading: boolean;
  error: string | null;
  checkStatus: () => Promise<void>;
}

export const useUserStatusCheck = (): UserStatusResult => {
  const [status, setStatus] = useState<'active' | 'inactive' | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const isCheckingRef = useRef(false);

  const checkStatus = useCallback(async (): Promise<void> => {
    if (!user?.id_ctacte) {
      setError('Usuario no válido');
      setStatus('active');
      return;
    }

    if (isCheckingRef.current) {
      return;
    }

    isCheckingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const codCtaCte = user.id_ctacte.toString();
      const attributesResponse: ColaboradorAttributesResponse = await fetchColaboradorAttributes(codCtaCte);

      if (attributesResponse.success && attributesResponse.data) {
        const statusAttribute = attributesResponse.data.find(attr => attr.CODATR === 'C05');

        if (statusAttribute) {
          const userStatus = statusAttribute.CODATRVAL === '2' ? 'active' : 'inactive';
          setStatus(userStatus);
        } else {
          setStatus('active');
        }
      } else {
        console.warn('Failed to get user attributes, defaulting to active status');
        setStatus('active');
      }
    } catch (err) {
      console.error('Error checking user status:', err);
      setStatus('active');
    } finally {
      setLoading(false);
      isCheckingRef.current = false;
    }
  }, [user?.id_ctacte]);

  useEffect(() => {
    if (user?.id_ctacte) {
      checkStatus();
    }
  }, [user?.id_ctacte, checkStatus]);

  return {
    status,
    loading,
    error,
    checkStatus
  };
};
