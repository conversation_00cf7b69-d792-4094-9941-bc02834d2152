
export const USER_ENDPOINTS = {
  LIST: '/users',
  DETAIL: (id: string | number) => `/users/${id}`,
  CREATE: '/users',
  UPDATE: (id: string | number) => `/users/${id}`,
  DELETE: (id: string | number) => `/users/${id}`,
};

export const CLIENT_ENDPOINTS = {
  LIST: '/clients',
  DETAIL: (id: string | number) => `/clients/${id}`,
  CREATE: '/clients',
  UPDATE: (id: string | number) => `/clients/${id}`,
  DELETE: (id: string | number) => `/clients/${id}`,
};

export const DASHBOARD_ENDPOINTS = {
  STATS: '/dashboard/s',
  RECENT_ACTIVITY: '/dashboard/r',
  CHART_DATA: '/dashboard/c',
};

export const AUTH_ENDPOINTS = {
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REGISTER: '/auth/register',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  VERIFY_EMAIL: '/auth/verify-email',
};
export const ENDPOINTS = {
  USER: USER_ENDPOINTS,
  CLIENT: CLIENT_ENDPOINTS,
  DASHBOARD: DASHBOARD_ENDPOINTS,
  AUTH: AUTH_ENDPOINTS,
};

export default ENDPOINTS;
