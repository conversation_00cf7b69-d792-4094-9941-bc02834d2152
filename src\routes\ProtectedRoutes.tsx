import { Routes, Route, Navigate, useParams } from 'react-router-dom';
import { RouteMiddleware } from '@/routes/RouteMiddleware';
import Layout from '@/components/layout/Layout';
import DynamicRoute from '@/routes/DynamicRoute';
import TokenVerifier from '@/routes/TokenVerifier';
import { MAIN_ROUTE_PREFIX, ROUTES } from '@/routes/routeConstants';
import { routes } from '@/routes/PageMetadata';

export const ProtectedRoutes = () => {
  const protectedRoutes = routes.filter(route => route.meta?.requiresAuth);
  return (
    <Routes>
      <Route element={<Layout />}>
        <Route element={<RouteMiddleware />}>
          {protectedRoutes.map(route => (
            <Route
              key={route.key}
              path={route.path}
              element={<route.component />}
            />
          ))}

          <Route path={MAIN_ROUTE_PREFIX} element={
            <DynamicRoute defaultPath={ROUTES.DASHBOARD} />
          } />

          <Route path={`${MAIN_ROUTE_PREFIX}/:url`} element={
            <TokenVerifier>
              <DynamicRoute />
            </TokenVerifier>
          } />

          <Route path=":url" element={
            <RedirectToMainPrefix />
          } />
        </Route>
      </Route>
    </Routes>
  );
};

const RedirectToMainPrefix = () => {
  const { url } = useParams<{ url: string }>();
  return <Navigate to={`${MAIN_ROUTE_PREFIX}/${url}`} replace />;
};

export default ProtectedRoutes;