import React, { useState, useEffect } from 'react';
import { LogoSidebar } from '../sidebar';
import { FA6 } from '@/config/icons/iconUtils';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { useLayout } from '@/context/LayoutContext';
import { getDefaultProfileImage } from '@/config/assetConfig';

import { DdSearchbar, DdNotification, DdMessage, DdProfile, DdLanguage } from './dropdowns';

const HorizontalHeader: React.FC = () => {
  const {
    settings,
    toggleTheme
  } = useTheme();

  const { handleSidebarResponsive } = useLayout();
  const { user } = useAuth();

  const [showSearchModal, setShowSearchModal] = useState(false);
  const [showAppsDropdown, setShowAppsDropdown] = useState(false);
  const [showNotificationsDropdown, setShowNotificationsDropdown] = useState(false);
  const [showMessagesDropdown, setShowMessagesDropdown] = useState(false);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);


  const toggleDropdown = (setter: React.Dispatch<React.SetStateAction<boolean>>) => {
    const allSetters = [
      setShowAppsDropdown,
      setShowNotificationsDropdown,
      setShowMessagesDropdown,
      setShowLanguageDropdown
    ];

    allSetters.forEach(currentSetter => {
      if (currentSetter !== setter) {
        currentSetter(false);
      }
    });

    setter(prev => !prev);
  };

  useEffect(() => {
    if (showAppsDropdown || showNotificationsDropdown ||
        showMessagesDropdown || showLanguageDropdown) {
      document.body.classList.add('dropdown-open');
    } else {
      document.body.classList.remove('dropdown-open');
    }

    return () => {
      document.body.classList.remove('dropdown-open');
    };
  }, [showAppsDropdown, showNotificationsDropdown, showMessagesDropdown, showLanguageDropdown]);

  return (
    <>
      <DdSearchbar isOpen={showSearchModal} onClose={() => setShowSearchModal(false)} />

      <nav className="navbar navbar-expand-xl container-fluid">
        <ul className="navbar-nav gap-2 align-items-center">
          <li className="nav-item d-block d-xl-none">
            <a className="nav-link sidebartoggler ms-n3" id="sidebarCollapse" href="#" onClick={(e) => { e.preventDefault(); handleSidebarResponsive(); }}>
              <FA6.FaBars />
            </a>
          </li>
          <li className="nav-item d-none d-xl-block">
            <LogoSidebar />
          </li>

          <li className="nav-item d-none d-lg-block search-box">
            <a
              className="nav-link nav-icon-hover d-none d-md-flex waves-effect waves-dark"
              href="#"
              onClick={(e) => { e.preventDefault(); setShowSearchModal(true); }}
            >
              <FA6.FaMagnifyingGlass />
            </a>
          </li>

          <li className="nav-item hover-dd d-none d-lg-block dropdown">
            <a
              className="nav-link nav-icon-hover"
              id="drop2"
              href="#"
              aria-haspopup="true"
              aria-expanded={showAppsDropdown}
              onClick={(e) => { e.preventDefault(); toggleDropdown(setShowAppsDropdown); }}
            >
              <FA6.FaTableCellsLarge />
            </a>
            {showAppsDropdown && (
              <div className="dropdown-menu dropdown-menu-nav dropdown-menu-animate-up py-0" aria-labelledby="drop2">
                <div className="p-3">
                  <h6 className="mb-0">Apps</h6>
                </div>
                <div className="p-3 border-top">
                  <div className="row g-3">
                    <div className="col-4">
                      <a href="#" className="text-center">
                        <FA6.FaBell className="fs-4 mb-1" />
                        <div className="text-truncate">Notifications</div>
                      </a>
                    </div>
                    <div className="col-4">
                      <a href="#" className="text-center">
                        <FA6.FaEnvelope className="fs-4 mb-1" />
                        <div className="text-truncate">Messages</div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </li>
        </ul>

        <a
          className="navbar-toggler nav-icon-hover p-0 border-0 text-white"
          href="#"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
          onClick={(e) => e.preventDefault()}
        >
          <span className="p-2">
            <FA6.FaEllipsis className="fs-7" />
          </span>
        </a>

        <div className="collapse navbar-collapse justify-content-end" id="navbarNav">
          <div className="d-flex align-items-center justify-content-between">
            <ul className="navbar-nav gap-2 flex-row ms-auto align-items-center justify-content-center">
              {/* Language Dropdown */}
              <li className="nav-item hover-dd dropdown nav-icon-hover-bg rounded-circle">
                <a
                  className="nav-link"
                  href="#"
                  id="langDropdown"
                  aria-expanded={showLanguageDropdown}
                  onClick={(e) => { e.preventDefault(); toggleDropdown(setShowLanguageDropdown); }}
                >
                  <FA6.FaGlobe className="fs-6" />
                </a>
                {showLanguageDropdown && (
                  <div className="dropdown-menu dropdown-menu-end dropdown-menu-animate-up" aria-labelledby="langDropdown">
                    <DdLanguage />
                  </div>
                )}
              </li>

              {/* Theme Toggle */}
              <li className="nav-item nav-icon-hover-bg rounded-circle">
                <a className="nav-link nav-icon-hover" href="#" onClick={(e) => { e.preventDefault(); toggleTheme(); }}>
                  {settings.theme === 'light' ? (
                    <FA6.FaMoon className="fs-6" />
                  ) : (
                    <FA6.FaSun className="fs-6" />
                  )}
                </a>
              </li>

              {/* Notifications */}
              <li className="nav-item hover-dd dropdown nav-icon-hover-bg rounded-circle d-none d-lg-block">
                <a
                  className="nav-link nav-icon-hover waves-effect waves-dark"
                  href="#"
                  id="notifDropdown"
                  aria-expanded={showNotificationsDropdown}
                  onClick={(e) => { e.preventDefault(); toggleDropdown(setShowNotificationsDropdown); }}
                >
                  <FA6.FaBell />
                  <div className="notify">
                    <span className="heartbit"></span>
                    <span className="point"></span>
                  </div>
                </a>
                {showNotificationsDropdown && (
                  <div
                    className="dropdown-menu py-0 content-dd dropdown-menu-animate-up dropdown-menu-end overflow-hidden"
                    aria-labelledby="notifDropdown"
                  >
                    <DdNotification />
                  </div>
                )}
              </li>

              {/* Messages */}
              <li className="nav-item hover-dd dropdown nav-icon-hover-bg rounded-circle d-none d-lg-block">
                <a
                  className="nav-link nav-icon-hover"
                  href="#"
                  id="msgDropdown"
                  aria-expanded={showMessagesDropdown}
                  onClick={(e) => { e.preventDefault(); toggleDropdown(setShowMessagesDropdown); }}
                >
                  <FA6.FaInbox />
                  <div className="notify">
                    <span className="heartbit"></span>
                    <span className="point"></span>
                  </div>
                </a>
                {showMessagesDropdown && (
                  <div
                    className="dropdown-menu py-0 content-dd dropdown-menu-animate-up dropdown-menu-end overflow-hidden"
                    aria-labelledby="msgDropdown"
                  >
                    <DdMessage />
                  </div>
                )}
              </li>

              {/* Profile */}
              <li className="nav-item dropdown">
                <a
                  className="nav-link nav-icon-hover"
                  href="#"
                  id="profileDropdown"
                  role="button"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <img
                    src={user?.avatar || getDefaultProfileImage()}
                    alt={`${user?.datosBLaboro?.Nombre || 'Usuario'} avatar`}
                    className="rounded-circle"
                    width="30"
                    height="30"
                  />
                </a>
                <DdProfile />
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </>
  );
};

export default HorizontalHeader;