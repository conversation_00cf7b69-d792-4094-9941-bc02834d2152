import React, { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { Menu } from '../../ui';
import { buildNavigationFromRoutes, routes } from '@/routes/PageMetadata';
import { useTheme } from '@/context/ThemeContext';
import { useSidebar } from '@/hooks';
import { useLayout } from '@/context';
import UserProfile from './UserProfile';

interface SidebarProps {
  show?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ show = true }) => {
  const location = useLocation();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const { settings: themeSettings } = useTheme();
  const { toggleSidebarVisibility } = useSidebar();
  const { windowWidth, breakpointValues } = useLayout();
  useEffect(() => {
    function handleLinkClick(e: MouseEvent) {
      const target = e.target as HTMLElement;
      if (
        windowWidth < breakpointValues.xl &&
        sidebarRef.current &&
        sidebarRef.current.contains(target) &&
        target.closest('a')
      ) {
        toggleSidebarVisibility();
      }
    }

    document.addEventListener('click', handleLinkClick);
    return () => document.removeEventListener('click', handleLinkClick);
  }, [toggleSidebarVisibility, windowWidth, breakpointValues.xl]);

  if (!show) {
    return null;
  }

  const navigationItems = buildNavigationFromRoutes(routes);
  navigationItems.sort((a, b) => {
    const routeA = routes.find(r => r.key === a.key);
    const routeB = routes.find(r => r.key === b.key);
    const orderA = routeA?.meta?.menuOrder || 999;
    const orderB = routeB?.meta?.menuOrder || 999;
    return orderA - orderB;
  });

  return (
    <div
      ref={sidebarRef}
      className="scrollbar"
      data-theme={themeSettings.theme}
    >
      {/* Sidebar Scroll */}
      <div className="scroll-sidebar">
        <UserProfile />
        {navigationItems.length > 0 ? (
          <Menu menuItems={navigationItems} location={location} />
        ) : (
          <div className="p-3 text-center text-muted">
            <p>No hay menús disponibles</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;