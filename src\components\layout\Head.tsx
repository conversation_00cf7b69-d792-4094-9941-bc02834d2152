import React from 'react';
import { Helmet } from 'react-helmet';
import { useContext } from 'react';
import { PageContext } from '../../context/PageContext';

interface HeadProps {
  title?: string;
  description?: string;
}

const Head: React.FC<HeadProps> = ({ 
  title: defaultTitle = 'System', 
  description: defaultDescription = 'System' 
}) => {
  const { pageMeta } = useContext(PageContext);
  const title = pageMeta?.title || defaultTitle;
  const description = pageMeta?.description || defaultDescription;

  return (
    <Helmet>
      {/* Etiquetas meta requeridas */}
      <meta charSet="UTF-8" />
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="description" content={description} />
      {/* Favicon */}
      <link
        rel="shortcut icon"
        type="image/png"
        href="/assets/images/logos/favicon.png"
      />
      {/* T<PERSON><PERSON><PERSON> de la página */}
      <title>{title}</title>
    </Helmet>
  );
};

export default Head;
