import React, { useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { FA6 } from '@/config/icons/iconUtils';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { useDropdown } from '@/hooks';
import { getDefaultProfileImage, getBackgroundPath } from '@/config/assetConfig';
import { MAIN_ROUTE_PREFIX } from '@/routes/routeConstants';

const UserProfile: React.FC = () => {
  const { isOpen, toggle, dropdownRef } = useDropdown(false);
  const { settings } = useTheme();
  const { user, logout, getFullName } = useAuth();
  const usernameRef = useRef<HTMLAnchorElement>(null);

  const fullName = user ? getFullName() : 'Usuario';
  const shouldShowTooltip = fullName.length > 25; // Show tooltip for names longer than 25 characters (increased due to more space)

  // Manage sidebar visibility when dropdown is open
  useEffect(() => {
    const sidebarElement = document.querySelector('.left-sidebar');
    if (sidebarElement) {
      if (isOpen) {
        sidebarElement.classList.add('user-dropdown-open');
      } else {
        sidebarElement.classList.remove('user-dropdown-open');
      }
    }

    // Cleanup on unmount
    return () => {
      if (sidebarElement) {
        sidebarElement.classList.remove('user-dropdown-open');
      }
    };
  }, [isOpen]);

  useEffect(() => {
    // Initialize Bootstrap tooltip for long names
    if (shouldShowTooltip && usernameRef.current) {
      const element = usernameRef.current;

      // Set tooltip attributes
      element.setAttribute('data-bs-toggle', 'tooltip');
      element.setAttribute('data-bs-placement', 'top');
      element.setAttribute('data-bs-title', fullName);

      // Initialize tooltip if Bootstrap is available
      let tooltip: any = null;
      if ((window as any).bootstrap?.Tooltip) {
        tooltip = new (window as any).bootstrap.Tooltip(element, {
          trigger: 'hover focus'
        });
      }

      return () => {
        if (tooltip) {
          tooltip.dispose();
        }
        // Clean up attributes
        element.removeAttribute('data-bs-toggle');
        element.removeAttribute('data-bs-placement');
        element.removeAttribute('data-bs-title');
      };
    }
  }, [fullName, shouldShowTooltip]);

  const handleToggleDropdown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggle();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && isOpen) {
      toggle();
      usernameRef.current?.focus(); // Return focus to trigger element
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && isOpen) {
        toggle();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown as any);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown as any);
    };
  }, [isOpen, toggle]);

  return (
    <div
      className={`user-profile position-relative ${isOpen ? 'dropdown-open' : ''}`}
      style={{
        backgroundImage: `url(${getBackgroundPath('profile')})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}>
      <div className="profile-img">
        <img
          src={user?.avatar || getDefaultProfileImage()}
          alt={`${user?.datosBLaboro?.Nombre || 'Usuario'} avatar`}
          className="w-100 rounded-circle overflow-hidden"
        />
      </div>

      <div className="profile-text hide-menu pt-1 dropdown" ref={dropdownRef} style={{ position: 'relative', zIndex: 30 }}>
        <a
          ref={usernameRef}
          href="#"
          className="dropdown-toggle u-dropdown w-100 text-white d-block position-relative nodecoration"
          onClick={handleToggleDropdown}
          onKeyDown={handleKeyDown}
          aria-expanded={isOpen}
          aria-haspopup="true"
          id="dropdownMenuLink"
          data-bs-toggle="dropdown"
          role="button"
          tabIndex={0}
        >
          {fullName}
        </a>

        <div
          className={`dropdown-menu ${isOpen ? 'show' : ''} nodecoration`}
          aria-labelledby="dropdownMenuLink"
          role="menu"
          style={{
            backgroundColor: settings.theme === 'dark' ? '#152332' : 'white',
            zIndex: 30,
            position: 'absolute'
          }}
          data-theme={settings.theme}
        >
          <Link
            to={`${MAIN_ROUTE_PREFIX}/miperfil`}
            className="dropdown-item d-flex gap-3 text-decoration-none"
            role="menuitem"
            tabIndex={isOpen ? 0 : -1}
          >
            <FA6.FaUser className="text-info" />
            Mi Perfil
          </Link>

          <div className="dropdown-divider" role="separator"></div>
          <a
            href="#"
            onClick={(e) => {
              e.preventDefault();
              logout();
            }}
            className="dropdown-item d-flex gap-3 text-decoration-none"
            role="menuitem"
            tabIndex={isOpen ? 0 : -1}
          >
            <FA6.FaRightFromBracket className="text-danger" />
            Desconectarse
          </a>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
