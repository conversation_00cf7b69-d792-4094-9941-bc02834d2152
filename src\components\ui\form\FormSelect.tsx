
import React, { forwardRef, useState, useEffect } from 'react';
import { Form, InputGroup } from 'react-bootstrap';

export type SelectOption = {
  value: string | number;
  label: string;
  disabled?: boolean;
  group?: string;
};

export interface FormSelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange' | 'size' | 'value'> {
  id?: string;
  name: string;
  label?: string;
  helpText?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  size?: 'sm' | 'lg';
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  options: SelectOption[];
  value?: string | number | string[] | number[];  
  multiple?: boolean;  
  searchable?: boolean;  
  noOptionsMessage?: string; 
  noResultsMessage?: string;  
  searchPlaceholder?: string; 
  placeholder?: string;
  onChange?: (value: string | number | string[] | number[]) => void;
  onFocus?: (e: React.FocusEvent<HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLSelectElement>) => void;}

const FormSelect = forwardRef<HTMLSelectElement, FormSelectProps>(
  (
    {
      id,
      name,
      label,
      helpText,
      error,
      required = false,
      disabled = false,
      size,
      startIcon,
      endIcon,
      options = [],
      value,
      multiple = false,
      searchable = false,
      noOptionsMessage = 'No hay opciones disponibles',
      noResultsMessage = 'No se encontraron resultados',
      searchPlaceholder = 'Buscar...',
      placeholder = 'Seleccione una opción',
      onChange,
      onFocus,
      onBlur,
      ...rest
    },
    ref
  ) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredOptions, setFilteredOptions] = useState<SelectOption[]>(options);
    const selectId = id || `form-select-${name}`;
    const hasStartAddon = !!startIcon;
    const hasEndAddon = !!endIcon;
    const hasAddons = hasStartAddon || hasEndAddon;
    useEffect(() => {
      if (searchable && searchTerm) {
        const filtered = options.filter(option =>
          option.label.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setFilteredOptions(filtered);
      } else {
        setFilteredOptions(options);
      }
    }, [searchTerm, options, searchable]);
    const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
      if (onChange) {
        if (multiple) {
          const selectedValues = Array.from(e.target.selectedOptions).map(option => option.value);
          onChange(selectedValues);
        } else {
          onChange(e.target.value);
        }
      }
    };

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
    };
    const groupedOptions: Record<string, SelectOption[]> = {};
    filteredOptions.forEach(option => {
      const group = option.group || '';
      if (!groupedOptions[group]) {
        groupedOptions[group] = [];
      }
      groupedOptions[group].push(option);
    });
    const renderSelect = () => {
      const normalizedValue = value ?
        (Array.isArray(value) ? value.map(v => String(v)) : String(value)) :
        (multiple ? [] : '');

      const selectProps = {
        id: selectId,
        name,
        disabled,
        required,
        multiple,
        value: normalizedValue,
        onChange: handleChange,
        onFocus,
        onBlur,
        className: `form-select${error ? ' is-invalid' : ''}${size ? ` form-select-${size}` : ''}`,
        ...rest
      };

      const selectElement = (
        <Form.Select ref={ref} {...selectProps}>
          {!multiple && <option value="">{placeholder}</option>}

          {Object.keys(groupedOptions).length === 0 && (
            <option disabled>{noOptionsMessage}</option>
          )}

          {Object.entries(groupedOptions).map(([group, groupOptions]) => {
            if (group) {
              return (
                <optgroup key={group} label={group}>
                  {groupOptions.map(option => (
                    <option
                      key={option.value}
                      value={option.value}
                      disabled={option.disabled}
                    >
                      {option.label}
                    </option>
                  ))}
                </optgroup>
              );
            }

            return groupOptions.map(option => (
              <option
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ));
          })}

          {searchable && filteredOptions.length === 0 && searchTerm && (
            <option disabled>{noResultsMessage}</option>
          )}
        </Form.Select>
      );

      if (hasAddons) {
        return (
          <InputGroup size={size}>
            {startIcon && <InputGroup.Text>{startIcon}</InputGroup.Text>}
            {selectElement}
            {endIcon && <InputGroup.Text>{endIcon}</InputGroup.Text>}
          </InputGroup>
        );
      }

      return selectElement;
    };

    return (
      <div className="mb-3">
        {label && (
          <label
            htmlFor={selectId}
            className={`form-label${required ? ' required' : ''}`}
          >
            {label}
          </label>
        )}

        {searchable && (
          <Form.Control
            type="text"
            placeholder={searchPlaceholder}
            value={searchTerm}
            onChange={handleSearchChange}
            className="mb-2"
            size={size}
          />
        )}

        {renderSelect()}

        {error && (
          <div className="invalid-feedback d-block">
            {error}
          </div>
        )}

        {helpText && (
          <div className="form-text text-muted">
            {helpText}
          </div>
        )}
      </div>
    );
  }
);

FormSelect.displayName = 'FormSelect';

export default FormSelect;
