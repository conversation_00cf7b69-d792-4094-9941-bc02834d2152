
export type ThemeMode = 'light' | 'dark';
export type ColorTheme = 'Blue_Theme' | 'Aqua_Theme' | 'Purple_Theme' | 'Green_Theme' | 'Cyan_Theme' | 'Orange_Theme';
export const THEME_STORAGE_KEY = 'app_theme_settings';
export const defaultThemeSettings = {
  theme: (import.meta.env.VITE_DEFAULT_THEME || 'light') as ThemeMode,
  colorTheme: (import.meta.env.VITE_DEFAULT_COLOR_THEME || 'Aqua_Theme') as ColorTheme,
  cardBorder: false
};

export const themeColors = {
  Blue_Theme: {
    primary: '#5D87FF',
    secondary: '#49BEFF',
    success: '#13DEB9',
    info: '#539BFF',
    warning: '#FFAE1F',
    danger: '#FA896B',
    light: '#F6F9FC',
    dark: '#2A3547'
  },
  Aqua_Theme: {
    primary: '#0074BA',
    secondary: '#47D7BC',
    success: '#13DEB9',
    info: '#539BFF',
    warning: '#FFAE1F',
    danger: '#FA896B',
    light: '#F6F9FC',
    dark: '#2A3547'
  },
  Purple_Theme: {
    primary: '#763EBD',
    secondary: '#95CFD5',
    success: '#13DEB9',
    info: '#539BFF',
    warning: '#FFAE1F',
    danger: '#FA896B',
    light: '#F6F9FC',
    dark: '#2A3547'
  },
  Green_Theme: {
    primary: '#0A7EA4',
    secondary: '#CCDA4E',
    success: '#13DEB9',
    info: '#539BFF',
    warning: '#FFAE1F',
    danger: '#FA896B',
    light: '#F6F9FC',
    dark: '#2A3547'
  },
  Cyan_Theme: {
    primary: '#01C0C8',
    secondary: '#FB9678',
    success: '#13DEB9',
    info: '#539BFF',
    warning: '#FFAE1F',
    danger: '#FA896B',
    light: '#F6F9FC',
    dark: '#2A3547'
  },
  Orange_Theme: {
    primary: '#FA896B',
    secondary: '#0074BA',
    success: '#13DEB9',
    info: '#539BFF',
    warning: '#FFAE1F',
    danger: '#FA896B',
    light: '#F6F9FC',
    dark: '#2A3547'
  }
};

export const themeCssVariables = {
  light: {
    bodyBg: '#F6F9FC',
    cardBg: '#FFFFFF',
    textColor: '#2A3547',
    borderColor: 'rgba(0, 0, 0, 0.1)'
  },
  dark: {
    bodyBg: '#2A3547',
    cardBg: '#2F3B4C',
    textColor: '#FFFFFF',
    borderColor: 'rgba(255, 255, 255, 0.1)'
  }
};

export const dropdownVariables = {
  light: {
    dropdownBg: '#FFFFFF',
    dropdownColor: '#3A4752',
    dropdownLinkColor: '#3A4752',
    dropdownLinkHoverColor: '#5D87FF',
    dropdownLinkHoverBg: 'rgba(93, 135, 255, 0.1)'
  },
  dark: {
    dropdownBg: '#2F3B4C',
    dropdownColor: '#FFFFFF',
    dropdownLinkColor: '#E9ECEF',
    dropdownLinkHoverColor: '#FFFFFF',
    dropdownLinkHoverBg: 'rgba(255, 255, 255, 0.15)'
  }
};

export const themeAttributes = {
  theme: 'data-bs-theme',
  colorTheme: 'data-color-theme',
  cardBorder: 'data-card',
  layout: 'data-layout',
  sidebarType: 'data-sidebartype',
  boxedLayout: 'data-boxed-layout',
  direction: 'dir'
};

export const themeClasses = {
  light: 'light-theme',
  dark: 'dark-theme',
  themePrefix: 'theme-'
};

export default {
  defaultThemeSettings,
  themeColors,
  themeCssVariables,
  dropdownVariables,
  themeAttributes,
  themeClasses,
  THEME_STORAGE_KEY
};
