// ----------------------------------------------
// Horizontal Style
// ----------------------------------------------

html[data-layout="horizontal"] {
  $headerHeight: 60px;

  @media screen and (max-width:1199px) {
    #sidebarnav{
      padding-left: 15px !important;
    }
    
  }

  .topbar {
    z-index: 99;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
  }

  .app-header {
    .navbar {
      min-height: $headerHeight;
    }
  }

  .brand-logo {
    width: auto;
    margin-right: 20px;

    @media screen and (max-width:1199px) {
      margin-right: 0px;

    }
  }

  #sidebarnav {
    list-style: none;
    padding-left: 0;
  }

  // header OR sidebar visible on horizontal layout
  @include media-breakpoint-up(xl) {
    .with-vertical {
      display: none;
    }

    .with-horizontal {
      display: block;
    }
  }

  .notification {
    top: 20px;
  }

  .body-wrapper {
    padding-top: $headerHeight;
  }

  .body-wrapper>.container-fluid {
    padding-top: calc(#{$horizontal-pagewrapper-padding-top-sm} - 20px) !important;
    padding: 15px;
  }

  
  @media screen and (max-width:1199px) {
    .body-wrapper > .container-fluid{
      padding-top: calc(115px - 70px) !important;
    }
    
  }
  // }
}

// Desktop Screen
@include media-breakpoint-only(lg) {
  html[data-layout="horizontal"] {
    .body-wrapper>.container-fluid {
      padding: $horizontal-boxed-page-breadcrumb-container-padding-lg;
    }
  }
}

// Above Tablet
@include media-breakpoint-up(xl) {
  html[data-layout="horizontal"][data-boxed-layout="full"] { 

    // Horizontal with boxed layout //
    // Set width of topbar, sidebar & page-wrapper //
    .navbar,
    .scroll-sidebar,
    .body-wrapper>.container-fluid {
      position: relative;
      max-width: 100%;
      margin: 0 auto;
    }
  }

  html[data-layout="horizontal"][data-boxed-layout="boxed"] {

    // Horizontal with boxed layout //
    // Set width of topbar, sidebar & page-wrapper //
    .navbar,
    .scroll-sidebar,
    .body-wrapper>.container-fluid {
      position: relative;
      max-width: $boxedWidth;
      margin: 0 auto;
    }
  }
}

// Above Tablet
@include media-breakpoint-up(xl) {
  html[data-layout="horizontal"] {

    // Topbar
    //
    .app-header {
      transition: 0s;

      .navbar {
        padding: 0 15px;
      }
    }

    // Horizontal with boxed layout //

    // Set width of topbar, sidebar & page-wrapper //
    .navbar,
    .scroll-sidebar,
    .body-wrapper>.container-fluid {
      position: relative;
      max-width: $boxedWidth;
      margin: 0 auto;
    }

    // Sidebar
    //
    .sidebar-nav {
      #sidebarnav {
        flex-wrap: wrap;

        >.sidebar-item>.has-arrow:after {
          display: block;
        }

        .sidebar-item {
          .first-level {
            .sidebar-link {
              color: $dark !important;
            }
          }
        }
      }
    }

    // Left Sidebar //
    .left-sidebar {
      position: fixed;
      top: $headerHeight;
      width: 100%;
      height: auto;
      z-index: 45;
      transition: 0s;
      border-top: 1px solid var(--bs-border-color);
      border-bottom: 1px solid var(--bs-border-color);

      .scroll-sidebar {
        height: calc(#{$sidebar-height} - 5px);
        overflow-y: unset;
        padding: 6px 15px;
      }
    }

    // Sidebar Nav //

    .sidebar-nav {
      #sidebarnav {
        display: flex;
        width: 100%;
        margin: 0;
        gap: 3px;

        // Sidebar Item //
        >.sidebar-item {
          border-bottom: 0;

          &:last-child {
            margin-right: 0;
          }

          >.sidebar-link {
            gap: 12px;
            padding: 0 14px;
            height: 44px;
            font-weight: 500;

            .hide-menu {
              margin-left: 0;
            }

            iconify-icon {
              font-size: 18px;
            }

            &.has-arrow {
              padding-right: 32px !important;
            }
          }
        }

        >.sidebar-item {
          position: relative;
          width: auto;
          margin-bottom: 0;
          min-height: $sidebar-height;

          .first-level,
          .second-level {
            animation: menuDropdownShow 0.3s ease-in-out;
            transition: background-color 0.3s;
            background-color: var(--bs-white);
            border-radius: 8px;

            .sidebar-item>.sidebar-link .sidebar-icon {
              margin-right: 0;
              margin-left: 0;
              flex-shrink: 0;
            }

            .sidebar-item {
              position: relative;

              .sidebar-link {
                color: var(--bs-body-color);
                opacity: 1;
                font-weight: 400;

                &:hover {
                  background-color: rgba(var(--bs-secondary), 0.1);
                  color: var(--bs-secondary) !important;

                  &.has-arrow::after {
                    border-color: var(--bs-secondary);
                  }
                }

                iconify-icon {
                  font-size: 10px;
                  display: block;
                }


                &.active {
                  color: var(--bs-secondary) !important;
                }
              }

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }

        >.sidebar-item {
          >.has-arrow:after {
            transform: rotate(-135deg) translate(0, -50%);
            right: 15px;
            top: 21px;
          }

          &:last-child>.first-level {
            right: 0;
            left: auto;
          }

          >.two-column+.first-level {
            width: $sidebar-first-level-width;

            >.sidebar-item {
              float: left;
              vertical-align: top;
              width: 50%;
            }
          }
        }

        // Sidebar Item inner ul //

        >.sidebar-item ul {
          position: absolute;
          left: 0;
          top: calc(#{$sidebar-height} - 21px);
          width: $sidebar-li-width;
          padding-bottom: 0;
          display: none;
          z-index: 100;
          box-shadow: $horizontal-list-shadow;
          background: var(--bs-white);
          transition: cubic-bezier(0.075, 0.82, 0.165, 1);
        }

        // Mega Dropdown //

        >.mega-dropdown {
          position: static;

          .first-level {
            width: 100%;

            >li {
              width: 25%;
              float: left;
            }
          }
        }

        // ul first level //

        >.sidebar-item:hover ul.first-level,
        >.sidebar-item:hover ul.first-level.collapse {
          display: block;
        }

        >.sidebar-item ul.second-level {
          left: $sidebar-li-width;
          top: 0;
        }

        >.sidebar-item:last-child>.first-level ul.second-level,
        >.sidebar-item .first-level .right-side-dd ul.second-level {
          right: $sidebar-li-width;
          top: 0;
          left: auto;
        }

        ul.first-level>.sidebar-item:hover ul.second-level {
          display: block;
        }

        .nav-small-cap,
        .sidebar-footer,
        .user-pro,
        .badge {
          display: none;
        }
      }
    }
  }
}

// Tablet Layout
@include media-breakpoint-down(xl) {
  // Main wrapper //

  html[data-layout="horizontal"] {
    // Open sidebar

    &.show-sidebar {
      .left-sidebar {
        left: 0;
      }
    }

    .left-sidebar {
      .sidebar-nav {
        .sidebar-item {
          .collapse {
            .sidebar-item {
              .sidebar-link {
                iconify-icon {
                  font-size: 16px;
                }
              }
            }
          }
        }
      }
    }

    .app-header {
      .navbar {
        .navbar-collapse {
          padding: 0;
        }
      }
    }
  }

}

@-webkit-keyframes menuDropdownShow {
  0% {
    opacity: 0;
    transform: translateY(-0.5rem);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes menuDropdownShow {
  0% {
    opacity: 0;
    transform: translateY(-0.5rem);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

