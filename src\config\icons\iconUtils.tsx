
/**
 * Utilidades de Iconos
 *
 * Este archivo proporciona una forma estandarizada de usar iconos en toda la aplicación.
 * Soporta múltiples bibliotecas y formatos de iconos:
 *
 * 1. React Icons - Para componentes React (recomendado para nuevo desarrollo)
 * 2. Tabler Icons - Para iconos basados en CSS (del template original)
 * 3. Iconify Icons - Para iconos basados en HTML (del template original)
 */

// Importar bibliotecas de iconos de React
import * as Fa6Icons from 'react-icons/fa6';
import * as RiIcons from 'react-icons/ri';
import * as HiIcons from 'react-icons/hi';
import React from 'react';

// Crear objetos de biblioteca exportables directamente
export const FA6 = Fa6Icons;
export const RI = RiIcons;
export const HI = HiIcons;

// Función de utilidad para obtener un icono de FA6 por nombre (string)
export const getFA6Icon = (iconName: string) => {
  if (iconName in Fa6Icons) {
    return Fa6Icons[iconName as keyof typeof Fa6Icons];
  }
  return null;
};

// Organizar todas las bibliotecas bajo un solo objeto para mayor comodidad
export const Icons = {
  FA6: Fa6Icons,
  RI: RiIcons,
  HI: HiIcons
};

/**
 * Componente TablerIcon
 *
 * Un componente React que envuelve los iconos de Tabler (basados en CSS)
 *
 * @example
 * <TablerIcon name="user" className="fs-6" />
 */
export const TablerIcon: React.FC<{ name: string; className?: string }> = ({
  name,
  className = ''
}) => {
  return <i className={`ti ti-${name} ${className}`}></i>;
};

/**
 * Componente IconifyIcon
 *
 * Un componente React que envuelve los iconos de Iconify (basados en HTML)
 *
 * @example
 * <IconifyIcon icon="solar:user-linear" className="fs-6" />
 */
export const IconifyIcon: React.FC<{ icon: string; className?: string }> = ({
  icon,
  className = ''
}) => {
  return <span className={`iconify-icon ${className}`} data-icon={icon}></span>;
};

/**
 * Obtiene el componente de icono apropiado basado en el nombre del icono
 *
 * Esta función ayuda a estandarizar el uso de iconos proporcionando un único punto de entrada
 * para todos los tipos de iconos. Detecta automáticamente el tipo de icono basado en el nombre.
 *
 * @example
 * const UserIcon = getIcon('user'); // Devuelve TablerIcon
 * const UserIcon = getIcon('FaUser'); // Devuelve FA6.FaUser
 * const UserIcon = getIcon('solar:user-linear'); // Devuelve IconifyIcon
 */
export function getIcon(iconName: string, props: Record<string, any> = {}) {
  // Verificar si es un icono de React Icons (comienza con letra mayúscula)
  if (/^[A-Z]/.test(iconName)) {
    // Verificar a qué biblioteca pertenece
    if (iconName.startsWith('Fa')) {
      const IconComponent = FA6[iconName as keyof typeof FA6];
      return IconComponent ? <IconComponent {...props} /> : null;
    } else if (iconName.startsWith('Ri')) {
      const IconComponent = RI[iconName as keyof typeof RI];
      return IconComponent ? <IconComponent {...props} /> : null;
    } else if (iconName.startsWith('Hi')) {
      const IconComponent = HI[iconName as keyof typeof HI];
      return IconComponent ? <IconComponent {...props} /> : null;
    }
  }

  // Verificar si es un icono de Iconify (contiene dos puntos)
  if (iconName.includes(':')) {
    return <IconifyIcon icon={iconName} className={props.className} />;
  }

  // Por defecto, usar Tabler icon
  return <TablerIcon name={iconName} className={props.className} />;
}
