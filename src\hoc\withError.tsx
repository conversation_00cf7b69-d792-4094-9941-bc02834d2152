import React from 'react';

interface WithErrorProps {
  error: Error | string | null;
  onRetry?: () => void;
  errorComponent?: React.ReactNode;
}


export function withError<P extends object>(
  Component: React.ComponentType<P>
): React.FC<P & WithErrorProps> {
  return function WithErrorComponent({
    error,
    onRetry,
    errorComponent,
    ...props
  }: WithErrorProps & P) {
    if (error) {
      if (errorComponent) {
        return <>{errorComponent}</>;
      }
      
      const errorMessage = typeof error === 'string' ? error : error.message;
      
      return (
        <div className="alert alert-danger" role="alert">
          <div className="d-flex align-items-center">
            <div className="me-3">
              <i className="ti ti-alert-circle fs-4"></i>
            </div>
            <div>
              <h5 className="mb-1">Error</h5>
              <p className="mb-2">{errorMessage}</p>
              {onRetry && (
                <button 
                  className="btn btn-sm btn-outline-danger" 
                  onClick={onRetry}
                >
                  Reintentar
                </button>
              )}
            </div>
          </div>
        </div>
      );
    }

    return <Component {...props as P} />;
  };
}

export default withError;
