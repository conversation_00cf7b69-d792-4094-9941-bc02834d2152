import { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth, useApi } from '../context';
import { RequestOptions } from '../context/ApiContext';
import { useLocalStorage } from './useLocalStorage';

export interface UseAuthenticatedApiOptions {
  enableCache?: boolean;
  cacheExpiry?: number;
  autoRenewToken?: boolean;
  maxRenewAttempts?: number;
  onError?: (error: Error) => void;
}

export interface RequestState<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
  status: number | null;
  timestamp: number | null;
}

export const useAuthenticatedApi = (options: UseAuthenticatedApiOptions = {}) => {
  const {
    enableCache = true,
    cacheExpiry = 5 * 60 * 1000,
    autoRenewToken = true,
    maxRenewAttempts = 1,
    onError = (error: Error) => console.error(error)
  } = options;
  const { user, isAuthenticated, logout } = useAuth();
  const api = useApi();
  const [isLoading, setIsLoading] = useState(false);

  const tokenRenewalPromiseRef = useRef<Promise<boolean> | null>(null);
  const renewalAttemptsRef = useRef(0);
  const [requestCache, setRequestCache] = useLocalStorage<Record<string, {
    data: any;
    timestamp: number;
    status: number;
  }>>('api_request_cache', {}, {
    expiry: cacheExpiry
  });

  const incrementActiveRequests = useCallback(() => {
    setIsLoading(true);
  }, []);

  const decrementActiveRequests = useCallback(() => {
    setIsLoading(false);
  }, []);

  const getCacheKey = useCallback((method: string, url: string, params?: Record<string, any>): string => {
    const queryString = params ? new URLSearchParams(params as Record<string, string>).toString() : '';
    return `${method}:${url}${queryString ? `?${queryString}` : ''}`;
  }, []);

  const renewToken = useCallback(async (): Promise<boolean> => {
    if (tokenRenewalPromiseRef.current) {
      return tokenRenewalPromiseRef.current;
    }
    if (renewalAttemptsRef.current >= maxRenewAttempts) {
      return false;
    }
    renewalAttemptsRef.current += 1;
    const renewalPromise = (async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        return true;
      } catch (error) {
        onError(new Error(`Error renovando token: ${error instanceof Error ? error.message : String(error)}`));
        return false;
      } finally {

        tokenRenewalPromiseRef.current = null;
      }
    })();

    tokenRenewalPromiseRef.current = renewalPromise;

    return renewalPromise;
  }, [api, maxRenewAttempts, onError]);


  const withAuthHeaders = useCallback((options: RequestOptions = {}): RequestOptions => {
    if (!isAuthenticated || !user) {
      return options;
    }
    const token = localStorage.getItem('auth_token') || '';
    return {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`
      }
    };
  }, [isAuthenticated, user]);

  const handleAuthError = useCallback(async <T>(error: any, retryFn: () => Promise<T>): Promise<T> => {
    if (error.status === 401) {
      if (autoRenewToken) {
        const renewed = await renewToken();
        if (renewed) {
          return retryFn();
        }
      }
      logout();
      throw new Error('Tu sesión ha expirado. Por favor, inicia sesión nuevamente.');
    }
    throw error;
  }, [logout, autoRenewToken, renewToken]);

  const createInitialRequestState = useCallback(<T = any>(): RequestState<T> => ({
    data: null,
    loading: false,
    error: null,
    status: null,
    timestamp: null
  }), []);

  const isCacheValid = useCallback((cacheKey: string): boolean => {
    if (!enableCache) return false;

    const cachedResponse = requestCache[cacheKey];
    if (!cachedResponse) return false;

    const now = Date.now();
    return now - cachedResponse.timestamp < cacheExpiry;
  }, [enableCache, requestCache, cacheExpiry]);

  const saveToCache = useCallback((cacheKey: string, data: any, status: number): void => {
    if (!enableCache) return;

    setRequestCache(prev => ({
      ...prev,
      [cacheKey]: {
        data,
        timestamp: Date.now(),
        status
      }
    }));
  }, [enableCache, setRequestCache]);

  const request = useCallback(async <T = any>(
    method: string,
    url: string,
    data?: any,
    options?: Omit<RequestOptions, 'method' | 'body'>
  ): Promise<T> => {
    incrementActiveRequests();
    const cacheKey = getCacheKey(method, url, options?.params);
    if (method === 'GET' && isCacheValid(cacheKey)) {
      const cachedResponse = requestCache[cacheKey];
      decrementActiveRequests();
      return cachedResponse.data as T;
    }

    const performRequest = async (): Promise<T> => {
      try {
        const response = await api.request<T>(url, {
          ...withAuthHeaders(options),
          method: method as any, // Casting temporal
          body: data
        });

        if (method === 'GET') {
          saveToCache(cacheKey, response, 200);
        }

        return response;
      } catch (error) {
        return handleAuthError(error, performRequest);
      } finally {
        decrementActiveRequests();
      }
    };

    return performRequest();
  }, [
    api, withAuthHeaders, handleAuthError,
    incrementActiveRequests, decrementActiveRequests,
    getCacheKey, isCacheValid, saveToCache, requestCache
  ]);

  const get = useCallback(<T = any>(
    url: string,
    params?: Record<string, any>,
    options?: Omit<RequestOptions, 'method' | 'params'>
  ): Promise<T> => {
    return request<T>('GET', url, undefined, { ...options, params });
  }, [request]);

  const post = useCallback(<T = any>(
    url: string,
    data?: any,
    options?: Omit<RequestOptions, 'method' | 'body'>
  ): Promise<T> => {
    return request<T>('POST', url, data, options);
  }, [request]);

  const put = useCallback(<T = any>(
    url: string,
    data?: any,
    options?: Omit<RequestOptions, 'method' | 'body'>
  ): Promise<T> => {
    return request<T>('PUT', url, data, options);
  }, [request]);

  const patch = useCallback(<T = any>(
    url: string,
    data?: any,
    options?: Omit<RequestOptions, 'method' | 'body'>
  ): Promise<T> => {
    return request<T>('PATCH', url, data, options);
  }, [request]);

  const del = useCallback(<T = any>(
    url: string,
    options?: Omit<RequestOptions, 'method'>
  ): Promise<T> => {
    return request<T>('DELETE', url, undefined, options);
  }, [request]);

  const useRequest = useCallback(<T = any>(
    method: string,
    url: string,
    options: {
      params?: Record<string, any>;
      body?: any;
      headers?: Record<string, string>;
      autoFetch?: boolean;
    } = {}
  ): [RequestState<T>, (newParams?: Record<string, any>, newBody?: any) => Promise<void>] => {
    const [state, setState] = useState<RequestState<T>>(createInitialRequestState<T>());

    const execute = useCallback(async (newParams?: Record<string, any>, newBody?: any): Promise<void> => {
      setState(prev => ({ ...prev, loading: true, error: null }));
      try {
        const params = newParams || options.params;
        const body = newBody || options.body;
        let response: T;
        if (method === 'GET') {
          response = await get<T>(url, params, { headers: options.headers });
        } else if (method === 'POST') {
          response = await post<T>(url, body, { params, headers: options.headers });
        } else if (method === 'PUT') {
          response = await put<T>(url, body, { params, headers: options.headers });
        } else if (method === 'PATCH') {
          response = await patch<T>(url, body, { params, headers: options.headers });
        } else if (method === 'DELETE') {
          response = await del<T>(url, { params, headers: options.headers });
        } else {
          throw new Error(`Método HTTP no soportado: ${method}`);
        }
        setState({
          data: response,
          loading: false,
          error: null,
          status: 200,
          timestamp: Date.now()
        });
      } catch (error) {

        setState({
          data: null,
          loading: false,
          error: error instanceof Error ? error.message : String(error),
          status: (error as any).status || 500,
          timestamp: Date.now()
        });
      }
    }, [method, url, options, get, post, put, patch, del]);

    useEffect(() => {
      if (options.autoFetch) {
        execute();
      }
    }, [execute, options.autoFetch]);

    return [state, execute];
  }, [get, post, put, patch, del, createInitialRequestState]);

  return {
    isAuthenticated,
    isLoading,
    request,
    get,
    post,
    put,
    patch,
    delete: del,
    useRequest,
    withAuthHeaders,
    handleAuthError,
    clearCache: useCallback(() => setRequestCache({}), [setRequestCache])
  };
};

export default useAuthenticatedApi;
