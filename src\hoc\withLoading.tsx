import React from 'react';

interface WithLoadingProps {
  isLoading: boolean;
  loadingMessage?: string;
  loadingComponent?: React.ReactNode;
}

export function withLoading<P extends object>(
  Component: React.ComponentType<P>
): React.FC<P & WithLoadingProps> {
  return function WithLoadingComponent({
    isLoading,
    loadingMessage = 'Loading...',
    loadingComponent,
    ...props
  }: WithLoadingProps & P) {
    if (isLoading) {
      if (loadingComponent) {
        return <>{loadingComponent}</>;
      }
      
      return (
        <div className="d-flex justify-content-center align-items-center p-5">
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Cargando...</span>
            </div>
            {loadingMessage && (
              <p className="mt-2 text-muted">{loadingMessage}</p>
            )}
          </div>
        </div>
      );
    }

    return <Component {...props as P} />;
  };
}

export default withLoading;
