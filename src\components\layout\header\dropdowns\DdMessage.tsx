import React from 'react';
import { Link } from 'react-router-dom';
import { FA6 } from '@/config/icons/iconUtils';

interface Message {
  avatar: string;
  name: string;
  time: string;
  message: string;
}

const DdMessage: React.FC = () => {
  const messages: Message[] = [
    {
      avatar: '/assets/images/profile/user-1.jpg',
      name: '<PERSON><PERSON>',
      time: '9:30 AM',
      message: 'Just see the my new admin!'
    },
    {
      avatar: '/assets/images/profile/user-2.jpg',
      name: '<PERSON>',
      time: '9:10 AM',
      message: 'Just a reminder that you have event'
    },
    {
      avatar: '/assets/images/profile/user-3.jpg',
      name: '<PERSON>',
      time: '9:08 AM',
      message: 'You can customize this template as you want'
    },
    {
      avatar: '/assets/images/profile/user-4.jpg',
      name: '<PERSON>',
      time: '9:30 AM',
      message: 'Just see the my new admin!'
    },
    {
      avatar: '/assets/images/profile/user-5.jpg',
      name: '<PERSON>, <PERSON><PERSON> & Rishvi..',
      time: '9:10 AM',
      message: 'Just a reminder that you have event'
    },
    {
      avatar: '/assets/images/profile/user-6.jpg',
      name: 'Eliga Rush',
      time: '9:08 AM',
      message: 'You can customize this template as you want'
    }
  ];

  return (
    <>
      <div className="py-3 px-4 bg-secondary">
        <div className="mb-0 fs-6 fw-medium text-white">Messages</div>
        <div className="mb-0 fs-2 fw-medium text-white">You have 5 new messages</div>
      </div>
      <div className="message-body" data-simplebar>
        {messages.map((message, index) => (
          <Link
            key={index}
            to="#"
            className="p-3 d-flex align-items-center dropdown-item gap-3 border-bottom"
          >
            <span className="user-img position-relative d-inline-block">
              <img
                src={message.avatar}
                alt="user"
                className="rounded-circle w-100 round-40"
              />
              <span className="profile-status bg-success position-absolute rounded-circle"></span>
            </span>
            <div className="w-80 d-inline-block v-middle">
              <div className="d-flex align-items-center justify-content-between">
                <h6 className="mb-1">{message.name}</h6>
                <span className="fs-2 text-nowrap d-block text-muted">{message.time}</span>
              </div>
              <span className="fs-2 d-block text-truncate text-muted">{message.message}</span>
            </div>
          </Link>
        ))}
      </div>
      <div className="p-3">
        <Link 
          className="d-flex btn btn-secondary align-items-center justify-content-center gap-2" 
          to="/messages"
        >
          <span>Check all Messages</span>
          <FA6.FaArrowRight className="fs-6" />
        </Link>
      </div>
    </>
  );
};

export default DdMessage;