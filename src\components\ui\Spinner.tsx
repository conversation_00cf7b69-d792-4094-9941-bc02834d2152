import React from 'react';

export type SpinnerType = 'border' | 'grow';
export type SpinnerSize = 'sm' | 'md' | 'lg';
export type SpinnerColor = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'danger' 
  | 'warning' 
  | 'info' 
  | 'light' 
  | 'dark';


interface SpinnerProps {
  type?: SpinnerType;
  color?: SpinnerColor;
  size?: SpinnerSize;
  srText?: string;
  centered?: boolean;
  text?: string;
  className?: string;
  style?: React.CSSProperties;
  fullHeight?: boolean;
}

const Spinner: React.FC<SpinnerProps> = ({
  type = 'border',
  color = 'primary',
  size = 'md',
  srText = 'Cargando...',
  centered = false,
  text,
  className = '',
  style,
  fullHeight = false,
}) => {
  // Determinar las clases CSS basadas en las props
  const spinnerClasses = [
    `spinner-${type}`,
    color ? `text-${color}` : '',
    size === 'sm' ? `spinner-${type}-sm` : '',
    className
  ].filter(Boolean).join(' ');
  
  // Determinar el tamaño personalizado si es 'lg'
  const sizeStyle = size === 'lg' 
    ? { width: '3rem', height: '3rem', ...style } 
    : style;
  
  if (text) {
    return (
      <div className="d-flex align-items-center">
        <strong>{text}</strong>
        <div 
          className={`${spinnerClasses} ms-auto`} 
          role="status" 
          aria-hidden="true"
          style={sizeStyle}
        ></div>
      </div>
    );
  }
  
  if (centered || fullHeight) {
    const containerStyle: React.CSSProperties = fullHeight 
      ? { height: '100vh' } 
      : {};
      
    return (
      <div 
        className="d-flex justify-content-center align-items-center" 
        style={containerStyle}
      >
        <div 
          className={spinnerClasses} 
          role="status"
          style={sizeStyle}
        >
          <span className="visually-hidden">{srText}</span>
        </div>
      </div>
    );
  }
  
  return (
    <div 
      className={spinnerClasses} 
      role="status"
      style={sizeStyle}
    >
      <span className="visually-hidden">{srText}</span>
    </div>
  );
};

export default Spinner;
