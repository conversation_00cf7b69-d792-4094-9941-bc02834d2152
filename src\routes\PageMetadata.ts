import { ReactNode } from 'react';
import { PermissionLevel } from '@/config/authConfig';
import { MAIN_ROUTE_PREFIX } from './routeConstants';
import { validRoutesCache } from '@/utils/preloadValidRoutes';

export interface PagePermissions {
  requiredPermission: PermissionLevel;
  resourceId: string;
}

export interface PageMetadata {
  title: string;
  description?: string;
  icon?: ReactNode;
  showInMenu?: boolean;
  menuOrder?: number;
  path: string;
  requiresAuth?: boolean;
  permissions?: PagePermissions;
  parent?: string;
  section?: string;
}

export interface RouteDefinition {
  path: string;
  key: string;
  exact?: boolean;
  component: React.ComponentType<Record<string, unknown>>;
  parentKey?: string;
  meta?: {
    title: string;
    description?: string;
    icon?: React.ReactNode;
    showInMenu?: boolean;
    menuOrder?: number;
    requiresAuth?: boolean;
    permissions?: PagePermissions;
    section?: string;
  };
  children?: RouteDefinition[];
}

export interface PageContextType {
  pageMeta?: {
    title?: string;
    description?: string;
    icon?: React.ReactNode;
    breadcrumbs?: Array<{ label: string; path?: string }>;
  };
  setPageMeta: (meta: {
    title?: string;
    description?: string;
    icon?: React.ReactNode;
    breadcrumbs?: Array<{ label: string; path?: string }>;
  }) => void;
}

export interface MenuGroup {
  key: string;
  title: string;
  icon?: React.ReactNode;
  section?: string;
  menuOrder?: number;
  showInMenu?: boolean;
}

export interface NavigationItem {
  key: string;
  path: string;
  title: string;
  icon?: React.ReactNode;
  isActive?: boolean;
  children?: NavigationItem[];
  section?: string;
  isGroup?: boolean;
}

// Menu groups registry - populated by menuGroups configuration
export const menuGroups: Record<string, MenuGroup> = {};

export const pageMetadata: Record<string, PageMetadata> = {};

export const pageModules: Record<string, {
  component: React.ComponentType<Record<string, unknown>>;
  metadata: PageMetadata;
}> = {};

export const routes: RouteDefinition[] = [];

export const registerPageMetadata = (key: string, metadata: PageMetadata): void => {
  pageMetadata[key] = metadata;
};

export const registerMenuGroup = (key: string, group: MenuGroup): void => {
  menuGroups[key] = group;
};

export const registerPageModule = (
  key: string,
  component: React.ComponentType<Record<string, unknown>>,
  metadata: PageMetadata
): void => {
  pageModules[key] = { component, metadata };

  const route: RouteDefinition = {
    path: metadata.path || '',
    key,
    component,
    parentKey: metadata.parent,
    meta: {
      title: metadata.title,
      description: metadata.description,
      icon: metadata.icon,
      showInMenu: metadata.showInMenu,
      menuOrder: metadata.menuOrder,
      requiresAuth: metadata.requiresAuth,
      permissions: metadata.permissions,
      section: metadata.section
    }
  };

  const existingRouteIndex = routes.findIndex(r =>
    r.key.toLowerCase() === key.toLowerCase() ||
    (r.meta?.title && metadata.title && r.meta.title === metadata.title && r.path === metadata.path)
  );

  if (existingRouteIndex >= 0) {
    if (routes[existingRouteIndex].key !== key &&
        routes[existingRouteIndex].meta?.title === metadata.title &&
        routes[existingRouteIndex].path === metadata.path) {
      return;
    }

    routes[existingRouteIndex] = route;
  } else {
    if (metadata.path) {
      const samePathIndex = routes.findIndex(r => r.path === metadata.path);
      if (samePathIndex >= 0) {
        return;
      }
    }

    routes.push(route);
  }

  if (validRoutesCache.isInitialized && metadata.path) {
    validRoutesCache.paths.add(metadata.path);
    if (metadata.path.startsWith('/')) {
      validRoutesCache.paths.add(metadata.path.substring(1));
    }
  }
};

export const registerDynamicRoute = (
  url: string,
  component: React.ComponentType<Record<string, unknown>>,
  requiresAuth: boolean = true,
  permissions?: PagePermissions
): void => {
  const metadata: PageMetadata = {
    title: url.charAt(0).toUpperCase() + url.slice(1), // Capitalize first letter
    description: `Dynamic route for ${url}`,
    showInMenu: false,
    path: `/${url}`,
    requiresAuth,
    permissions
  };

  registerPageModule(url, component, metadata);
};

export const buildNavigationFromRoutes = (routeDefinitions: RouteDefinition[]): NavigationItem[] => {

  const routeMap: Record<string, RouteDefinition> = {};
  routeDefinitions.forEach(route => {
    routeMap[route.key] = route;
  });
  const topLevelRoutes = routeDefinitions
    .filter(route => route.meta?.showInMenu && !route.parentKey);

  const routeNavItems = topLevelRoutes
    .map(route => mapRouteToNavItem(route, routeDefinitions, routeMap));


  const groupNavItems: NavigationItem[] = [];
  Object.values(menuGroups).forEach(group => {
    if (!group.showInMenu) return;

    const childRoutes = routeDefinitions.filter(route =>
      route.parentKey === group.key && route.meta?.showInMenu
    );

    if (childRoutes.length > 0) {
      const groupIcon = group.icon || childRoutes[0]?.meta?.icon;

      const groupNavItem: NavigationItem = {
        key: group.key,
        path: '#', 
        title: group.title,
        icon: groupIcon,
        isGroup: true,
        section: group.section,
        children: childRoutes
          .map(route => mapRouteToNavItem(route, routeDefinitions, routeMap))
          .sort((a, b) => {
            const routeA = routeDefinitions.find(r => r.key === a.key);
            const routeB = routeDefinitions.find(r => r.key === b.key);
            const orderA = routeA?.meta?.menuOrder || 999;
            const orderB = routeB?.meta?.menuOrder || 999;
            return orderA - orderB;
          })
      };

      groupNavItems.push(groupNavItem);
    }
  });

  const allItems = [...routeNavItems, ...groupNavItems];

  if (allItems.length === 0) {
    console.warn(`[ROUTES] No navigation items generated`);
  }

  return allItems.sort((a, b) => {
    const orderA = a.isGroup
      ? menuGroups[a.key]?.menuOrder || 999
      : routeMap[a.key]?.meta?.menuOrder || 999;
    const orderB = b.isGroup
      ? menuGroups[b.key]?.menuOrder || 999
      : routeMap[b.key]?.meta?.menuOrder || 999;
    return orderA - orderB;
  });
};

const mapRouteToNavItem = (
  route: RouteDefinition,
  allRoutes: RouteDefinition[],
  routeMap: Record<string, RouteDefinition>
): NavigationItem => {
  const children = allRoutes
    .filter(r => r.parentKey === route.key && r.meta?.showInMenu)
    .map(r => mapRouteToNavItem(r, allRoutes, routeMap));

  const section = route.meta?.section || (
    route.key.includes('config') || route.key.includes('settings')
      ? 'CONFIGURACIÓN'
      : 'HOME'
  );

  const navItem = {
    key: route.key,
    path: route.path || '#',
    title: route.meta?.title || '',
    icon: route.meta?.icon,
    children: children.length > 0 ? children : undefined,
    section: section
  };

  return navItem;
};

export const getRoutesForUser = (): RouteDefinition[] => {
  return routes;
};

export const getValidRoutes = (): string[] => {
  const metadataPaths = Object.values(pageMetadata)
    .filter(p => p.path !== undefined)
    .map(p => p.path);

  const authRoutes = ['/auth', '/auth/login', '/auth/register', '/auth/forgot-password'];
  const prefixedRoutes = metadataPaths
    .filter(path => !path.startsWith('/auth'))
    .map(path => {
      if (path.startsWith(MAIN_ROUTE_PREFIX)) {
        return path;
      }
      return `${MAIN_ROUTE_PREFIX}${path.startsWith('/') ? path : '/' + path}`;
    });
  return [...metadataPaths, ...authRoutes, ...prefixedRoutes];
};

export const isValidRoute = (route: string): boolean => {
  const validRoutes = getValidRoutes();

  if (route === '/') return true;

  if (validRoutes.includes(route)) return true;

  const routeWithPrefix = route.startsWith('/')
    ? `${MAIN_ROUTE_PREFIX}${route}`
    : `${MAIN_ROUTE_PREFIX}/${route}`;

  const routeWithoutPrefix = route.startsWith(MAIN_ROUTE_PREFIX)
    ? route.substring(MAIN_ROUTE_PREFIX.length)
    : route;

  return validRoutes.includes(routeWithPrefix) || validRoutes.includes(routeWithoutPrefix);
};

export const getMetadataForRoute = (route: string): PageMetadata | undefined => {
  const directMatch = Object.values(pageMetadata).find(p => p.path === route);
  if (directMatch) return directMatch;

  const routeWithPrefix = route.startsWith('/')
    ? `${MAIN_ROUTE_PREFIX}${route}`
    : `${MAIN_ROUTE_PREFIX}/${route}`;

  const withPrefixMatch = Object.values(pageMetadata).find(p => p.path === routeWithPrefix);
  if (withPrefixMatch) return withPrefixMatch;

  if (route.startsWith(MAIN_ROUTE_PREFIX)) {
    const routeWithoutPrefix = route.substring(MAIN_ROUTE_PREFIX.length);
    return Object.values(pageMetadata).find(p => p.path === routeWithoutPrefix);
  }

  return undefined;
};

export const getPublicRoutes = (): string[] => {
  return Object.values(pageMetadata)
    .filter(p => !p.requiresAuth)
    .map(p => p.path);
};

export const getProtectedRoutes = (): string[] => {
  return Object.values(pageMetadata)
    .filter(p => p.requiresAuth)
    .map(p => p.path);
};

export default pageMetadata;
