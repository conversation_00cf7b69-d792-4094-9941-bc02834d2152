
export const ANIMATION_DURATION = {
  fast: 150,
  normal: 250,
  slow: 400,
  extraSlow: 600,
  scroll: 800,
  scrollSlow: 1200
};


export const ANIMATION_CLASSES = {
  // Animaciones de entrada
  fadeIn: 'fade-in',
  slideInUp: 'slide-in-up',
  slideInDown: 'slide-in-down',
  slideInLeft: 'slide-in-left',
  slideInRight: 'slide-in-right',
  zoomIn: 'zoom-in',
  
  // Animaciones de salida
  fadeOut: 'fade-out',
  slideOutUp: 'slide-out-up',
  slideOutDown: 'slide-out-down',
  slideOutLeft: 'slide-out-left',
  slideOutRight: 'slide-out-right',
  zoomOut: 'zoom-out',
  
  // Animaciones específicas de componentes
  dropdownMenuAnimateUp: 'dropdown-menu-animate-up',
  dropdownMenuAnimateDown: 'dropdown-menu-animate-down',
  dropdownMenuAnimateOut: 'dropdown-menu-animate-out',
  sidebarSlideIn: 'sidebar-slide-in',
  sidebarSlideOut: 'sidebar-slide-out',
  modalFadeIn: 'modal-fade-in',
  modalFadeOut: 'modal-fade-out'
};

// Funciones de timing para animaciones
export const TIMING_FUNCTIONS = {
  linear: 'linear',
  ease: 'ease',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
  easeInQuad: 'cubic-bezier(0.55, 0.085, 0.68, 0.53)',
  easeOutQuad: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  easeInOutQuad: 'cubic-bezier(0.455, 0.03, 0.515, 0.955)',
  // Modern easing functions for smoother animations
  easeOutCubic: 'cubic-bezier(0.4, 0, 0.2, 1)',
  easeInOutCubic: 'cubic-bezier(0.4, 0, 0.2, 1)',
  easeOutBack: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
  easeInBack: 'cubic-bezier(0.6, -0.28, 0.735, 0.045)',
  spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
};

// Configuración de animaciones para componentes específicos
export const COMPONENT_ANIMATIONS = {
  dropdown: {
    enterClass: ANIMATION_CLASSES.dropdownMenuAnimateUp,
    exitClass: ANIMATION_CLASSES.dropdownMenuAnimateOut,
    enterDuration: ANIMATION_DURATION.normal,
    exitDuration: ANIMATION_DURATION.fast,
    timingFunction: TIMING_FUNCTIONS.easeOutQuad
  },
  sidebar: {
    enterClass: ANIMATION_CLASSES.sidebarSlideIn,
    exitClass: ANIMATION_CLASSES.sidebarSlideOut,
    enterDuration: ANIMATION_DURATION.normal,
    exitDuration: ANIMATION_DURATION.normal,
    timingFunction: TIMING_FUNCTIONS.easeInOut
  },
  modal: {
    enterClass: ANIMATION_CLASSES.modalFadeIn,
    exitClass: ANIMATION_CLASSES.modalFadeOut,
    enterDuration: ANIMATION_DURATION.normal,
    exitDuration: ANIMATION_DURATION.fast,
    timingFunction: TIMING_FUNCTIONS.easeInOut
  },
  tooltip: {
    enterClass: ANIMATION_CLASSES.fadeIn,
    exitClass: ANIMATION_CLASSES.fadeOut,
    enterDuration: ANIMATION_DURATION.fast,
    exitDuration: ANIMATION_DURATION.fast,
    timingFunction: TIMING_FUNCTIONS.ease
  },
  detailExpansion: {
    enterClass: ANIMATION_CLASSES.slideInDown,
    exitClass: ANIMATION_CLASSES.slideOutUp,
    enterDuration: ANIMATION_DURATION.normal,
    exitDuration: ANIMATION_DURATION.fast,
    timingFunction: TIMING_FUNCTIONS.easeOutBack
  }
};


export const animationUtils = {

  animateIn: (
    element: HTMLElement | null,
    animationClass: string = ANIMATION_CLASSES.dropdownMenuAnimateUp,
    duration: number = ANIMATION_DURATION.normal
  ): Promise<void> => {
    return new Promise((resolve) => {
      if (!element) {
        resolve();
        return;
      }
      element.classList.add(animationClass);
      const handleAnimationEnd = () => {
        element.removeEventListener('animationend', handleAnimationEnd);
        resolve();
      };
      element.addEventListener('animationend', handleAnimationEnd);
      setTimeout(() => {
        resolve();
      }, duration);
    });
  },

  animateOut: (
    element: HTMLElement | null,
    animationClass: string = ANIMATION_CLASSES.dropdownMenuAnimateOut,
    duration: number = ANIMATION_DURATION.fast
  ): Promise<void> => {
    return new Promise((resolve) => {
      if (!element) {
        resolve();
        return;
      }

      element.classList.add(animationClass);
      
      const handleAnimationEnd = () => {
        element.classList.remove(animationClass);
        element.removeEventListener('animationend', handleAnimationEnd);
        resolve();
      };    
      element.addEventListener('animationend', handleAnimationEnd);
      setTimeout(() => {
        element.classList.remove(animationClass);
        resolve();
      }, duration);
    });
  },

  transition: (
    element: HTMLElement | null,
    properties: Record<string, string>,
    duration: number = ANIMATION_DURATION.normal,
    timingFunction: string = TIMING_FUNCTIONS.easeInOut
  ): Promise<void> => {
    return new Promise((resolve) => {
      if (!element) {
        resolve();
        return;
      }
      const originalTransition = element.style.transition;
      element.style.transition = `all ${duration}ms ${timingFunction}`;
      setTimeout(() => {
        Object.entries(properties).forEach(([property, value]) => {
          (element.style as any)[property] = value;
        });
      }, 10);

      const handleTransitionEnd = () => {
        element.style.transition = originalTransition;
        element.removeEventListener('transitionend', handleTransitionEnd);
        resolve();
      };
      element.addEventListener('transitionend', handleTransitionEnd);
      setTimeout(() => {
        element.style.transition = originalTransition;
        resolve();
      }, duration + 50);
    });
  },

  smoothScrollTo: (
    target: HTMLElement | number,
    options: {
      offset?: number;
      duration?: number;
      easing?: string;
      callback?: () => void;
    } = {}
  ): Promise<void> => {
    return new Promise((resolve) => {
      const {
        offset = 0,
        duration = ANIMATION_DURATION.scroll,
        easing = TIMING_FUNCTIONS.easeOutCubic,
        callback
      } = options;

      const reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

      if (reducedMotion) {
        const scrollTop = typeof target === 'number'
          ? target
          : window.pageYOffset + target.getBoundingClientRect().top - offset;

        window.scrollTo({ top: Math.max(0, scrollTop) });
        callback?.();
        resolve();
        return;
      }

      const startPosition = window.pageYOffset;
      const targetPosition = typeof target === 'number'
        ? target
        : window.pageYOffset + target.getBoundingClientRect().top - offset;

      const distance = targetPosition - startPosition;

      // If the distance is very small, use shorter duration
      const adjustedDuration = Math.abs(distance) < 100 ? duration * 0.5 : duration;

      let startTime: number | null = null;

      const animateScroll = (currentTime: number) => {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / adjustedDuration, 1);

        // Apply enhanced easing functions with smoother curves
        let easedProgress: number;

        switch (easing) {
          case TIMING_FUNCTIONS.easeOutCubic:
            // Enhanced cubic easing for smoother deceleration
            easedProgress = 1 - Math.pow(1 - progress, 3);
            break;
          case TIMING_FUNCTIONS.easeOutBack:
            const c1 = 1.70158;
            const c3 = c1 + 1;
            easedProgress = 1 + c3 * Math.pow(progress - 1, 3) + c1 * Math.pow(progress - 1, 2);
            break;
          case TIMING_FUNCTIONS.easeInOutQuad:
            // Smoother quad easing
            easedProgress = progress < 0.5
              ? 2 * progress * progress
              : 1 - Math.pow(-2 * progress + 2, 2) / 2;
            break;
          case TIMING_FUNCTIONS.easeInOutCubic:
            // Additional smooth cubic option
            easedProgress = progress < 0.5
              ? 4 * progress * progress * progress
              : 1 - Math.pow(-2 * progress + 2, 3) / 2;
            break;
          default:
            easedProgress = progress;
        }

        const currentPosition = startPosition + distance * easedProgress;
        window.scrollTo(0, currentPosition);

        if (timeElapsed < adjustedDuration) {
          requestAnimationFrame(animateScroll);
        } else {
          // Ensure we end exactly at the target position
          window.scrollTo(0, targetPosition);
          callback?.();
          resolve();
        }
      };

      requestAnimationFrame(animateScroll);
    });
  }
};

export default {
  ANIMATION_DURATION,
  ANIMATION_CLASSES,
  TIMING_FUNCTIONS,
  COMPONENT_ANIMATIONS,
  animationUtils
};
