import React, { useEffect, useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { pageModules, isValidRoute } from '@/routes/PageMetadata';
import { Spinner } from '@/components/ui';
import { ROUTES, MAIN_ROUTE_PREFIX } from '@/routes/routeConstants';
import logger from '@/utils/logger';

interface DynamicRouteProps {
  defaultPath?: string;
}

const DynamicRoute: React.FC<DynamicRouteProps> = ({ defaultPath }) => {
  const { url } = useParams<{ url: string }>();
  const [loading, setLoading] = useState(true);
  const [Component, setComponent] = useState<React.ComponentType | null>(null);
  const [notFound, setNotFound] = useState(false);

  useEffect(() => {
    const findComponent = async () => {
      if (defaultPath && !url) {
        setComponent(() => () => <Navigate to={defaultPath} replace />);
        setLoading(false);
        return;
      }
      setLoading(true);
      setComponent(null);
      setNotFound(false);

      if (!url) {
        logger.error('DYNAMIC_ROUTE', 'No URL parameter provided');
        setNotFound(true);
        setLoading(false);
        return;
      }

      if (!isValidRoute(url)) {
        logger.error('DYNAMIC_ROUTE', `Invalid route: "${url}" - not found in preloaded routes`);
        setNotFound(true);
        setLoading(false);
        return;
      }
      const directMatch = Object.entries(pageModules).find(
        ([_, { metadata }]) => metadata?.path === `/${url}` || metadata?.path === `${MAIN_ROUTE_PREFIX}/${url}`
      );

      if (directMatch) {
        const [, { component }] = directMatch;
        setComponent(() => component);
        setLoading(false);
        return;
      }

      const keyMatch = pageModules[url];
      if (keyMatch) {
        setComponent(() => keyMatch.component);
        setLoading(false);
        return;
      }

      setNotFound(true);
      setLoading(false);
    };

    findComponent();
  }, [url, defaultPath]);

  if (loading) {
    return <Spinner fullHeight centered color="primary" />;
  }

  if (notFound) {
    return <Navigate to={ROUTES.ERROR} replace />;
  }

  if (!Component) {
    return <Navigate to={ROUTES.ERROR} replace />;
  }

  return <Component />;
};

export default DynamicRoute;
