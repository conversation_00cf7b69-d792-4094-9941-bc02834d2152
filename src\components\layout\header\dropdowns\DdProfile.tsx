import React from 'react';
import { useUser } from '@/context';
import { MAIN_ROUTE_PREFIX } from '@/routes/routeConstants';
import { getDefaultProfileImage } from '@/config/assetConfig';

const DdProfile: React.FC = () => {
  const { user, logout, getFullName } = useUser();

  const profileLinks = [
    { title: 'Mi Perfil', url: `${MAIN_ROUTE_PREFIX}/miperfil` },
    { title: 'Movimientos', url: `${MAIN_ROUTE_PREFIX}/estadodecuenta` }
  ];

  const handleLogout = (e: React.MouseEvent) => {
    e.preventDefault();
    logout();
  };

  const handleNavigation = (url: string, e: React.MouseEvent) => {
    e.preventDefault();
    window.location.href = `#${url}`;
  };

  return (
    <div className="dropdown-menu dropdown-menu-end dropdown-menu-animate-up" data-simplebar>
      <div className="py-3 border-bottom">
        <div className="d-flex align-items-center px-3">
          <img
            src={getDefaultProfileImage()}
            className="rounded-circle"
            width="50"
            height="50"
            alt={`${user?.datosBLaboro?.Nombre || 'Usuario'}`}
          />
          <div className="ms-3">
            <h6 className="mb-1">{user ? getFullName() : 'Usuario'}</h6>
            <p className="mb-0 fs-2 text-muted">{user?.email || 'Sin correo'}</p>
          </div>
        </div>
      </div>

      <div className="py-3">
        {profileLinks.map((link, index) => (
          <a
            key={index}
            href={`#${link.url}`}
            className="dropdown-item py-2 px-3 rounded-2 mx-3 mb-1"
            onClick={(e) => handleNavigation(link.url, e)}
          >
            {link.title}
          </a>
        ))}

        <div className="border-top pt-3 mt-2">
          <a
            href="#"
            onClick={handleLogout}
            className="btn btn-secondary w-100 mx-3"
            style={{ width: 'calc(100% - 1.5rem)' }}
          >
            Desconectarse
          </a>
        </div>
      </div>
    </div>
  );
};

export default DdProfile;
