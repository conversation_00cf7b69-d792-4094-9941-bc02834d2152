import { useState, useCallback } from 'react';
import { useUserStatusCheck } from './useUserStatusCheck';
import { useDatosPersonales } from './useDatosPersonales';

interface UseInactiveUserHandlerOptions {
  onStatusChange?: (status: 'active' | 'inactive' | null) => void;
  autoRefreshPersonalData?: boolean;
}

interface UseInactiveUserHandlerResult {
  userStatus: 'active' | 'inactive' | null;
  statusLoading: boolean;
  isVerifying: boolean;
  handleVerifyStatus: () => Promise<void>;
  isInactive: boolean;
}

export const useInactiveUserHandler = (
  options: UseInactiveUserHandlerOptions = {}
): UseInactiveUserHandlerResult => {
  const { onStatusChange, autoRefreshPersonalData = true } = options;
  const [isVerifying, setIsVerifying] = useState(false);
  
  const { status: userStatus, loading: statusLoading, checkStatus } = useUserStatusCheck();
  const datosPersonales = useDatosPersonales();

  const handleVerifyStatus = useCallback(async () => {
    if (statusLoading || isVerifying) {
      return;
    }

    setIsVerifying(true);
    try {
      const promises = [checkStatus()];
      
      if (autoRefreshPersonalData) {
        promises.push(datosPersonales.refetchDatosPersonales());
      }
      
      await Promise.all(promises);
      
      if (onStatusChange) {
        onStatusChange(userStatus);
      }
    } catch (error) {
      console.error('[useInactiveUserHandler] Error verifying status:', error);
    } finally {
      setIsVerifying(false);
    }
  }, [statusLoading, isVerifying, checkStatus, autoRefreshPersonalData, datosPersonales, onStatusChange, userStatus]);

  return {
    userStatus,
    statusLoading,
    isVerifying,
    handleVerifyStatus,
    isInactive: userStatus === 'inactive'
  };
};
