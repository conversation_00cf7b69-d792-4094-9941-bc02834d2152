import { useState, useCallback, useEffect, useRef } from 'react';

export const useDropdown = (initialState: boolean = false) => {
  const [isOpen, setIsOpen] = useState(initialState);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const open = useCallback(() => {
    setIsOpen(true);
  }, []);
  const close = useCallback(() => {
    setIsOpen(false);
  }, []);
  const toggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && isOpen) {
        close();
      }
    };
    const handleTouchOutside = (event: TouchEvent) => {
      if (dropdownRef.current && event.touches[0] &&
          !dropdownRef.current.contains(event.touches[0].target as Node) && isOpen) {
        close();
      }
    };
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleTouchOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleTouchOutside);
    };
  }, [isOpen, close]);
  return {
    isOpen,
    open,
    close,
    toggle,
    dropdownRef
  };
};

export default useDropdown;
