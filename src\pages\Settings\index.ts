import SettingsComponent from './Settings';
import withPageMetadata from '@/hoc/withPageMetadata';
import { registerPageModule } from '@/routes/PageMetadata';

const Settings = withPageMetadata(SettingsComponent);

if (SettingsComponent.pageMetadata) {
  try {
    registerPageModule('Settings', SettingsComponent, {
      ...SettingsComponent.pageMetadata,
      path: SettingsComponent.pageMetadata.path || '/main/settings'
    });
  } catch (error) {
    console.error('[SETTINGS] Error registering Settings page module:', error);
  }
}

// Export the component's metadata directly
export const pageMetadata = SettingsComponent.pageMetadata;

export default Settings;
