
import { callProcedure } from '../api/procedureService';
import { DatosPersonasBL } from '../context/AuthContext';

export interface DatosPersonalesBLResponse {
  mensaje: string;
  success: boolean;
  data: DatosPersonasBL[];
}

export async function fetchDatosPersonalesBL(nroDoc: string): Promise<DatosPersonasBL | null> {
  try {
    if (!nroDoc) {
      throw new Error('Missing required parameter: nroDoc');
    }
    const response = await callProcedure<DatosPersonalesBLResponse>('SP_GET_DATOS_PERSONAS_BL', {
      '@CUIL': nroDoc
    }, 'sql');

    if (!response.success) {
      return null;
    }

    if (!response.data || response.data.length === 0) {
      return null;
    }

    const datosPersonales = response.data[0];
    return datosPersonales;
  } catch (error) {
    return null;
  }
}


export function validateDatosPersonalesBL(datos: any): datos is DatosPersonasBL {
  return (
    datos &&
    typeof datos.IdPersona === 'number' &&
    typeof datos.Codigo === 'string' &&
    typeof datos.Nombre === 'string' &&
    typeof datos.DomicilioCalle === 'string' &&
    typeof datos.DomicilioLoc === 'string' &&
    typeof datos.DocNro === 'string' &&
    typeof datos.Sexo === 'string' &&
    typeof datos.FecNacimiento === 'string' &&
    typeof datos.Telefono === 'string' &&
    typeof datos.CUIL === 'string' &&
    typeof datos.EmailPersonal === 'string'
  );
}

export function formatDatosPersonalesBL(datos: DatosPersonasBL) {
  const formatSafeDate = (dateStr: string): string => {
    if (!dateStr) return '';
    try {
      let dateObj: Date;
      if (dateStr.includes('-')) {
        const [year, month, day] = dateStr.split('-').map((num: string) => parseInt(num, 10));
        dateObj = new Date(year, month - 1, day);
      } else {
        dateObj = new Date(dateStr);
      }
      return dateObj.toLocaleDateString('es-AR');
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateStr;
    }
  };

  return {
    ...datos,
    FecNacimientoFormatted: formatSafeDate(datos.FecNacimiento),
    SexoFormatted: datos.Sexo === 'M' ? 'Masculino' : datos.Sexo === 'F' ? 'Femenino' : datos.Sexo,
    TelefonoFormatted: datos.Telefono || 'Sin información',
    EmailFormatted: datos.EmailPersonal || 'Sin información'
  };
}
