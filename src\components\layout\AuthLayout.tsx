import React from 'react';
import { Outlet } from 'react-router-dom';
import { useResponsive } from '@/hooks';
import { importedAssets } from '@/config/assetConfig';
import { AUTH_CONFIG } from '@/config/authConfig';

const AuthLayout: React.FC = () => {
  const { isMobile, isTablet } = useResponsive();
  const showBackground = !isMobile() && !isTablet();

  return (
    <div className="position-relative overflow-hidden radial-gradient min-vh-100 w-100">
      <div className="position-relative z-index-5">
        <div className="row">
          <div className="col-xl-5 col-xxl-4">
            <div className="authentication-login min-vh-100 bg-body row justify-content-center">
              <div className="col-12 text-center">
                <a href={AUTH_CONFIG.homePath} className="text-nowrap logo-img d-flex align-items-center justify-content-center gap-2 px-4 py-9 w-100">
                  <span className="logo-icon">
                    <img src={importedAssets.rayco.swooshDark} alt="logo" className="dark-logo" width="100" />
                  </span>
                  <span className="logo-icon">
                    <img src={importedAssets.rayco.mercosurLogo} alt="logo" className="dark-logo ps-2" width="100" />
                  </span>
                  <span className="logo-icon">
                    <img src={importedAssets.rayco.adidasBarDark} alt="logo" className="dark-logo ps-2" width="100" />
                  </span>
                </a>
              </div>
              <div className="auth-max-width col-sm-8 col-md-6 col-xl-7 px-4">
                <Outlet />
              </div>
            </div>
          </div>

          {/* Imagen de fondo (solo en pantallas grandes) */}
          {showBackground && (
            <div className="col-xl-7 col-xxl-8">
              <div className="d-flex align-items-center justify-content-center h-100" style={{backgroundColor: '#e5ecf9'}}>
                <img src={importedAssets.backgrounds.login} alt="Auth background" className="img-fluid" />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
