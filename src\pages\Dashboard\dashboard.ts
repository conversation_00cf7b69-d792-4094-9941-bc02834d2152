import { useEffect, useState } from 'react';
import { useApi, UseApiOptions, UseApiResult } from '@/api/useApi';

export interface DashboardStat {
  title: string;
  value: string;
  change: string;
  iconName: string;
  iconColor: string;
  positive: boolean;
  progress: number;
  subtitle: string;
}

export interface Activity {
  id: number;
  title: string;
  description: string;
  time: string;
  iconName: string;
  iconColor: string;
  type: string;
}

export interface Training {
  name: string;
  date: string;
  participants: string;
}

export interface Birthday {
  name: string;
  date: string;
  department: string;
}

export interface Reminder {
  id: number;
  title: string;
  description: string;
  iconName: string;
  iconColor: string;
  priority: 'high' | 'medium' | 'low';
}

export interface HRSummary {
  attendance: {
    percentage: string;
    label: string;
  };
  absences: {
    count: string;
    label: string;
  };
  vacations: {
    count: string;
    label: string;
  };
  contracts: {
    count: string;
    label: string;
  };
}

// Define the endpoints for dashboard-related operations
export const DASHBOARD_ENDPOINTS = {
  STATS: '/api/dashboard/stats',
  ACTIVITIES: '/api/dashboard/activities',
  TRAININGS: '/api/dashboard/trainings',
  BIRTHDAYS: '/api/dashboard/birthdays',
  REMINDERS: '/api/dashboard/reminders',
  HR_SUMMARY: '/api/dashboard/hr-summary',
};

// Mock data for dashboard stats
export const MOCK_DASHBOARD_STATS: DashboardStat[] = [
  {
    title: 'Estadodecuenta Activos',
    value: '248',
    change: '+5.1%',
    iconName: 'FaUsers',
    iconColor: 'primary',
    positive: true,
    progress: 85,
    subtitle: 'vs. trimestre anterior'
  },
  {
    title: 'Satisfacción Laboral',
    value: '87%',
    change: '+3.2%',
    iconName: 'FaFaceSmile',
    iconColor: 'success',
    positive: true,
    progress: 87,
    subtitle: 'encuesta mensual'
  },
  {
    title: 'Capacitaciones Completadas',
    value: '42',
    change: '+12%',
    iconName: 'FaGraduationCap',
    iconColor: 'info',
    positive: true,
    progress: 75,
    subtitle: 'este mes'
  },
  {
    title: 'Tasa de Retención',
    value: '94.5%',
    change: '+1.5%',
    iconName: 'FaHandshake',
    iconColor: 'warning',
    positive: true,
    progress: 94,
    subtitle: 'anual'
  }
];

// Mock data for recent activities
export const MOCK_ACTIVITIES: Activity[] = [
  {
    id: 1,
    title: 'Nueva contratación',
    description: 'Carlos Benítez se unió al equipo de Desarrollo',
    time: '10:45 AM',
    iconName: 'FaUserPlus',
    iconColor: 'success',
    type: 'hiring'
  },
  {
    id: 2,
    title: 'Capacitación completada',
    description: 'María González completó el curso de Liderazgo Efectivo',
    time: 'Ayer',
    iconName: 'FaGraduationCap',
    iconColor: 'primary',
    type: 'training'
  },
  {
    id: 3,
    title: 'Evaluación de desempeño',
    description: 'Completadas 15 evaluaciones del departamento de Marketing',
    time: 'Ayer',
    iconName: 'FaClipboardCheck',
    iconColor: 'warning',
    type: 'evaluation'
  },
  {
    id: 4,
    title: 'Solicitud de vacaciones',
    description: 'José Martínez solicitó vacaciones del 15 al 30 de julio',
    time: '2 días',
    iconName: 'FaCalendarDay',
    iconColor: 'info',
    type: 'vacation'
  },
  {
    id: 5,
    title: 'Aniversario laboral',
    description: 'Ana Rojas cumple 5 años en la empresa',
    time: '3 días',
    iconName: 'FaCakeCandles',
    iconColor: 'secondary',
    type: 'anniversary'
  },
  {
    id: 6,
    title: 'Nuevo beneficio',
    description: 'Lanzamiento del programa de bienestar para colaboradores',
    time: '1 semana',
    iconName: 'FaHeartPulse',
    iconColor: 'dark',
    type: 'benefit'
  }
];

// Mock data for trainings
export const MOCK_TRAININGS: Training[] = [
  { name: 'Liderazgo Efectivo', date: '15 Jul', participants: '12 inscritos' },
  { name: 'Gestión del Tiempo', date: '22 Jul', participants: '18 inscritos' },
  { name: 'Trabajo en Equipo', date: '5 Ago', participants: '15 inscritos' }
];

// Mock data for birthdays
export const MOCK_BIRTHDAYS: Birthday[] = [
  { name: 'Gabriela Sánchez', date: '15 Jul', department: 'Marketing' },
  { name: 'Javier Moreno', date: '22 Jul', department: 'Desarrollo' },
  { name: 'Patricia Villalba', date: '28 Jul', department: 'Recursos Humanos' }
];

// Mock data for reminders
export const MOCK_REMINDERS: Reminder[] = [
  {
    id: 1,
    title: 'Evaluaciones de desempeño',
    description: 'Vencen en 5 días',
    iconName: 'FaBell',
    iconColor: 'danger',
    priority: 'high'
  },
  {
    id: 2,
    title: 'Reunión de equipo',
    description: 'Mañana, 10:00 AM',
    iconName: 'FaBell',
    iconColor: 'warning',
    priority: 'medium'
  },
  {
    id: 3,
    title: 'Cierre de planilla',
    description: '28 de este mes',
    iconName: 'FaBell',
    iconColor: 'info',
    priority: 'low'
  }
];

// Mock data for HR summary
export const MOCK_HR_SUMMARY: HRSummary = {
  attendance: {
    percentage: '96%',
    label: 'Hoy'
  },
  absences: {
    count: '4',
    label: 'Colaboradores'
  },
  vacations: {
    count: '8',
    label: 'Pendientes'
  },
  contracts: {
    count: '3',
    label: 'Por renovar'
  }
};

/**
 * Hook for fetching dashboard stats
 *
 * @param options - Options for the API request
 * @returns Object with dashboard stats data, loading, error states and fetch/reset functions
 */
export function useDashboardStats(options?: UseApiOptions): UseApiResult<DashboardStat[]> {
  return useApi<DashboardStat[]>(DASHBOARD_ENDPOINTS.STATS, {
    ...options,
    mockData: MOCK_DASHBOARD_STATS
  });
}

/**
 * Hook for fetching recent activities
 *
 * @param options - Options for the API request
 * @returns Object with activities data, loading, error states and fetch/reset functions
 */
export function useRecentActivities(options?: UseApiOptions): UseApiResult<Activity[]> {
  return useApi<Activity[]>(DASHBOARD_ENDPOINTS.ACTIVITIES, {
    ...options,
    mockData: MOCK_ACTIVITIES
  });
}

/**
 * Hook for fetching upcoming trainings
 *
 * @param options - Options for the API request
 * @returns Object with trainings data, loading, error states and fetch/reset functions
 */
export function useUpcomingTrainings(options?: UseApiOptions): UseApiResult<Training[]> {
  return useApi<Training[]>(DASHBOARD_ENDPOINTS.TRAININGS, {
    ...options,
    mockData: MOCK_TRAININGS
  });
}

/**
 * Hook for fetching birthdays
 *
 * @param options - Options for the API request
 * @returns Object with birthdays data, loading, error states and fetch/reset functions
 */
export function useBirthdays(options?: UseApiOptions): UseApiResult<Birthday[]> {
  return useApi<Birthday[]>(DASHBOARD_ENDPOINTS.BIRTHDAYS, {
    ...options,
    mockData: MOCK_BIRTHDAYS
  });
}

/**
 * Hook for fetching reminders
 *
 * @param options - Options for the API request
 * @returns Object with reminders data, loading, error states and fetch/reset functions
 */
export function useReminders(options?: UseApiOptions): UseApiResult<Reminder[]> {
  return useApi<Reminder[]>(DASHBOARD_ENDPOINTS.REMINDERS, {
    ...options,
    mockData: MOCK_REMINDERS
  });
}

/**
 * Hook for fetching HR summary
 *
 * @param options - Options for the API request
 * @returns Object with HR summary data, loading, error states and fetch/reset functions
 */
export function useHRSummary(options?: UseApiOptions): UseApiResult<HRSummary> {
  return useApi<HRSummary>(DASHBOARD_ENDPOINTS.HR_SUMMARY, {
    ...options,
    mockData: MOCK_HR_SUMMARY
  });
}

/**
 * Hook that combines all dashboard data
 *
 * @param options - Options for the API requests
 * @returns Object with all dashboard data, loading state
 */
export function useDashboardData(options?: UseApiOptions) {
  const stats = useDashboardStats({ ...options, initialFetch: false });
  const activities = useRecentActivities({ ...options, initialFetch: false });
  const trainings = useUpcomingTrainings({ ...options, initialFetch: false });
  const birthdays = useBirthdays({ ...options, initialFetch: false });
  const reminders = useReminders({ ...options, initialFetch: false });
  const hrSummary = useHRSummary({ ...options, initialFetch: false });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      await Promise.all([
        stats.fetch(),
        activities.fetch(),
        trainings.fetch(),
        birthdays.fetch(),
        reminders.fetch(),
        hrSummary.fetch()
      ]);
      setLoading(false);
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    stats: stats.data,
    activities: activities.data,
    trainings: trainings.data,
    birthdays: birthdays.data,
    reminders: reminders.data,
    hrSummary: hrSummary.data,
    loading,
    refetch: async () => {
      setLoading(true);
      await Promise.all([
        stats.fetch(),
        activities.fetch(),
        trainings.fetch(),
        birthdays.fetch(),
        reminders.fetch(),
        hrSummary.fetch()
      ]);
      setLoading(false);
    }
  };
}

// Export all hooks and data individually
// Adding a default export that includes all the named exports
const dashboardModule = {
  // Data types
  DashboardStat: {},
  Activity: {},
  Training: {},
  Birthday: {},
  Reminder: {},
  HRSummary: {},

  // Constants
  DASHBOARD_ENDPOINTS,
  MOCK_DASHBOARD_STATS,
  MOCK_ACTIVITIES,
  MOCK_TRAININGS,
  MOCK_BIRTHDAYS,
  MOCK_REMINDERS,
  MOCK_HR_SUMMARY,

  // Hooks
  useDashboardStats,
  useRecentActivities,
  useUpcomingTrainings,
  useBirthdays,
  useReminders,
  useHRSummary,
  useDashboardData
};

export default dashboardModule;
