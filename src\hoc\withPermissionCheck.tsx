import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { PageMetadata } from './withPageMetadata';
import { AUTH_CONFIG } from '../config/authConfig';

type ComponentWithMetadata<P = any> = React.ComponentType<P> & {
  pageMetadata?: PageMetadata;
};

interface WithPermissionCheckOptions {
  redirectTo?: string;
  showAccessDenied?: boolean;
}

const AccessDenied: React.FC = () => (
  <div className="container mt-5">
    <div className="alert alert-danger">
      <h4 className="alert-heading">Acceso Denegado</h4>
      <p>No tienes permisos para acceder a esta página.</p>
    </div>
  </div>
);

export const withPermissionCheck = <P extends object>(
  Component: ComponentWithMetadata<P>,
  options: WithPermissionCheckOptions = {}
) => {
  const {
    redirectTo = AUTH_CONFIG.homePath,
    showAccessDenied = false
  } = options;

  const WithPermissionCheck = (props: P) => {
    const { isAuthenticated, hasPermission } = useAuth();

    const metadata = Component.pageMetadata;

    if (!metadata || !metadata.permissions) {
      return <Component {...props} />;
    }

    const { requiredPermission } = metadata.permissions;

    if (!requiredPermission) {
      return <Component {...props} />;
    }

    if (!isAuthenticated) {
      return <Navigate to="/login" />;
    }

    const path = metadata.path || window.location.pathname;
    const hasAccess = hasPermission(path, requiredPermission);

    if (!hasAccess) {
      return showAccessDenied ? <AccessDenied /> : <Navigate to={redirectTo} />;
    }

    return <Component {...props} />;
  };

  WithPermissionCheck.displayName = `WithPermissionCheck(${Component.displayName || Component.name || 'Component'})`;

  return WithPermissionCheck;
};

export default withPermissionCheck;
