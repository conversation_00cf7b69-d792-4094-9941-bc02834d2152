import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context';
import { AUTH_CONFIG } from '@/config/authConfig';

interface AuthMiddlewareProps {
  children: React.ReactNode;
  requiresAuth?: boolean;
  permissions?: {
    requiredPermission: number;
    resourceId: string;
  };
}

const AuthMiddleware: React.FC<AuthMiddlewareProps> = ({ 
  children, 
  requiresAuth = true,
  permissions 
}) => {
  const { isAuthenticated, hasPermission } = useAuth();
  const location = useLocation();

  if (requiresAuth && !isAuthenticated) {
    if (location.pathname.startsWith('/auth/')) {
      return <>{children}</>;
    }
    return <Navigate to={AUTH_CONFIG.loginPath} state={{ from: location }} replace />;
  }

  if (requiresAuth && isAuthenticated && permissions?.requiredPermission) {
    const resourceId = permissions.resourceId;
    const requiredPermission = permissions.requiredPermission;
    const hasAccess = hasPermission(resourceId, requiredPermission);
    if (!hasAccess) {
      const previousPage = location.state?.from?.pathname || AUTH_CONFIG.homePath;
      return <Navigate to={previousPage} state={{ from: location }} replace />;
    }
  }
  return <>{children}</>;
};

export default AuthMiddleware;
