#main-wrapper {
  min-height: 100vh;
  overflow-x: hidden;
  background-color:$main-body-bg;
}

.page-wrapper {
  .body-wrapper > .container-fluid {
    transition: 0.2s ease-in;
    padding: 0 24px;
    min-height: calc(100vh - 160px);
  }
}

.body-wrapper {
  padding-top: calc(#{$headerHeight} + 30px);
}

a {
  text-decoration: none;
}

ul {
  list-style: none;
  padding: 0;
}

.toast-onload{
  position: fixed;
  top: 24px;
  right: 24px;
  z-index: 999;
  max-width: 294px;
  width: 100%;
  transition: all .5s;
}



//
// Above Large screen
//
@include media-breakpoint-up(xl) {
  [data-layout="vertical"] {
    .topbar {
      left: 0;
    }

    body {
      .page-wrapper {
        margin-left: $sidebar-width-full;
      }
      &[data-sidebartype="mini-sidebar"] {
        .page-wrapper {
          margin-left: $sidebar-width-mini;
        }

        // Profile image positioning handled in _user-profile.scss

        .nav-small-cap {

          .nav-small-cap-icon {
            display: inline-block;
          }
        }

        .logo-img {
          width: 40px;
          overflow: hidden;
        }

        // Sidebar
        .sidebar-nav {
          .has-arrow::after {
            display: none;
          }
          .sidebar-list {
            .sidebar-list-item {
              text-align: center;
            }
          }
        }

        .left-sidebar {
          width: $sidebar-width-mini;
          box-shadow: 0 10px 20px rgba(0,0,0,0.12);
          .scroll-sidebar {
            height: calc(100vh - 50px);
          }
          .hide-menu,
          .sidebar-ad {
            display: none;
          }
          .sidebar-nav {
            ul {
              .sidebar-item {
                .sidebar-link{
                  padding: 11px 11px;
                }
              }
            }
          }

          &:hover {
            width: $sidebar-width-full;

            // Profile image positioning handled in _user-profile.scss

            .logo-img {
              width: 100%;
            }
            .nav-small-cap {

              .nav-small-cap-icon {
                display: none;
              }
            }
            z-index: 50;
            .hide-menu,
            .sidebar-nav .has-arrow::after,
            .sidebar-ad {
              display: block;
            }

            .sidebar-nav {
              .sidebar-list {
                .sidebar-list-item {
                  text-align: left;
                }
              }
            }
            .logo-img {
              width: 100%;
            }
          }
        }
      }
    }
  }
}


html[data-boxed-layout="boxed"] {
  .container-fluid {
    max-width: $boxedWidth;
    margin: 0 auto;
  }
}


.with-horizontal {
  display: none;
}

.with-vertical {
  display: block;
}


// Mobile Screen
//
@media (max-width: 767px) {
  //
  // Main wrapper
  #main-wrapper {
    // Sidebar type=mini-sidebar
    // ###############################################
    &[data-sidebartype="mini-sidebar"] {
      // Left sidebar
      //
      .left-sidebar,
      .left-sidebar .sidebar-footer {
        left: -$sidebar-width-full;
      }
    }

    // Open sidebar
    // ###############################################
    &.show-sidebar {
      .left-sidebar,
      .left-sidebar .sidebar-footer {
        left: 0;
      }
    }

    // Sidebar position fixed with vertical layout && both with fixed
    // ###############################################
    &[data-layout="vertical"][data-sidebar-position="fixed"],
    &[data-layout="vertical"][data-header-position="fixed"][data-sidebar-position="fixed"] {
      // Topbar
      //
      .topbar .top-navbar {
        .navbar-collapse {
          position: relative;
          top: $topbar-height;
        }
      }
    }
  }
}