import { useContext, useEffect, useState } from 'react';
import { useLocation, Navigate } from 'react-router-dom';
import { AuthRoutes } from '@/routes/AuthRoutes';
import { ProtectedRoutes } from '@/routes/ProtectedRoutes';
import { AuthContext } from '@/context/AuthContext';
import { preloadValidRoutes } from '@/utils/preloadValidRoutes';
import { loadPageMetadata } from '@/routes/routeUtils';
import { Spinner } from '@/components/ui';
import { ROUTES } from '@/routes/routeConstants';

export const AppRoutes = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [metadataLoaded, setMetadataLoaded] = useState(false);

  useEffect(() => {
    const initRoutes = async () => {
      setLoading(true);

      try {
        await loadPageMetadata();
        setMetadataLoaded(true);
        if (isAuthenticated) {
          preloadValidRoutes();
        }
      } catch (error) {
        console.error('[ROUTES] Error initializing routes:', error);
      } finally {
        setLoading(false);
      }
    };

    initRoutes();
  }, [isAuthenticated]);

  const isAuthPath = location.pathname.startsWith("/auth");
  const isRootPath = location.pathname === "/";

  if (loading) {
    return <Spinner fullHeight centered color="primary" />;
  }

  if (isRootPath && metadataLoaded) {
    if (isAuthenticated) {
      return <Navigate to={ROUTES.HOME} replace />;
    } else {
      return <Navigate to={ROUTES.LOGIN} replace />;
    }
  }

  if (isAuthPath || !isAuthenticated) {
    return <AuthRoutes />;
  }
  return <ProtectedRoutes />;
};

export default AppRoutes;