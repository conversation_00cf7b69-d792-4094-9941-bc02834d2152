html[data-card='border'] {
    .card {
        border: 1px solid var(--bs-border-color);
        box-shadow: none;
    }
}

.card {
    margin-bottom: var(--bs-gutter-x);
}

.card-title {
    font-size: 18px;
    color: var(--bs-card-title-color);
}

.card-subtitle {
    font-size: 14px;
    color: var(--bs-card-subtitle-color);
}

.card-hover {
    transition: 0.2s ease-in;

    &:hover {
        transform: translate3d(0px, -5px, 0px);
    }
}

.cardwithborder {
    .card {
        box-shadow: none;
        border: 1px solid var(--bs-border-color);
    }
}


.card-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    overflow: auto;
}


//
// Draggable Cards
//
.draggable-cards .card-header {
    cursor: move;
}

.card-moved .card {
    background: var(--bs-info);
    color: var(--bs-white);
}