import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLayout } from '@/context';
import { BREAKPOINTS, RESPONSIVE, Breakpoint, DIMENSIONS } from '@/config/responsiveConfig';

export type Orientation = 'portrait' | 'landscape';

export type BreakpointChangeCallback = (
  newBreakpoint: Breakpoint,
  oldBreakpoint: Breakpoint | null
) => void;

export const useResponsive = (options: {
  onBreakpointChange?: BreakpointChangeCallback;
} = {}) => {
  const { windowWidth, windowHeight } = useLayout();
  const prevBreakpointRef = useRef<Breakpoint | null>(null);
  const [orientation, setOrientation] = useState<Orientation>(
    windowWidth > windowHeight ? 'landscape' : 'portrait'
  );
  const isGreaterThan = useCallback((breakpoint: Breakpoint): boolean => {
    return windowWidth >= BREAKPOINTS[breakpoint];
  }, [windowWidth]);
  const isLessThan = useCallback((breakpoint: Breakpoint): boolean => {
    return windowWidth < BREAKPOINTS[breakpoint];
  }, [windowWidth]);

  const isBetween = useCallback((minBreakpoint: Breakpoint, maxBreakpoint: Breakpoint): boolean => {
    return windowWidth >= BREAKPOINTS[minBreakpoint] && windowWidth < BREAKPOINTS[maxBreakpoint];
  }, [windowWidth]);

  const isMobile = useCallback((): boolean => {
    return windowWidth < RESPONSIVE.mobileBreakpoint;
  }, [windowWidth]);

  const isTablet = useCallback((): boolean => {
    return windowWidth >= RESPONSIVE.mobileBreakpoint && windowWidth < RESPONSIVE.desktopBreakpoint;
  }, [windowWidth]);

  const isDesktop = useCallback((): boolean => {
    return windowWidth >= RESPONSIVE.desktopBreakpoint;
  }, [windowWidth]);

  useEffect(() => {
    setOrientation(windowWidth > windowHeight ? 'landscape' : 'portrait');
  }, [windowWidth, windowHeight]);

  const getCurrentBreakpoint = useCallback((): Breakpoint => {
    if (windowWidth < BREAKPOINTS.sm) return 'xs';
    if (windowWidth < BREAKPOINTS.md) return 'sm';
    if (windowWidth < BREAKPOINTS.lg) return 'md';
    if (windowWidth < BREAKPOINTS.xl) return 'lg';
    if (windowWidth < BREAKPOINTS.xxl) return 'xl';
    return 'xxl';
  }, [windowWidth]);

  const currentBreakpoint = useMemo(() => getCurrentBreakpoint(), [getCurrentBreakpoint]);

  useEffect(() => {
    const { onBreakpointChange } = options;
    if (onBreakpointChange && prevBreakpointRef.current !== currentBreakpoint) {
      onBreakpointChange(currentBreakpoint, prevBreakpointRef.current);
    }
    prevBreakpointRef.current = currentBreakpoint;
  }, [currentBreakpoint, options]);

  const mobileValue = useMemo(() => isMobile(), [isMobile]);
  const tabletValue = useMemo(() => isTablet(), [isTablet]);
  const desktopValue = useMemo(() => isDesktop(), [isDesktop]);

  const breakpointInfo = useMemo(() => ({
    isMobile: mobileValue,
    isTablet: tabletValue,
    isDesktop: desktopValue,
    isXs: isLessThan('sm'),
    isSm: isBetween('sm', 'md'),
    isMd: isBetween('md', 'lg'),
    isLg: isBetween('lg', 'xl'),
    isXl: isBetween('xl', 'xxl'),
    isXxl: isGreaterThan('xxl'),
    currentBreakpoint,
    orientation
  }), [
    mobileValue, tabletValue, desktopValue,
    isLessThan, isBetween, isGreaterThan,
    currentBreakpoint, orientation
  ]);

  const getResponsiveClasses = useCallback((
    classMap: {
      xs?: string;
      sm?: string;
      md?: string;
      lg?: string;
      xl?: string;
      xxl?: string;
      mobile?: string;
      tablet?: string;
      desktop?: string;
    }
  ): string => {
    const classes: string[] = [];

    if (classMap.xs && isLessThan('sm')) classes.push(classMap.xs);
    if (classMap.sm && isBetween('sm', 'md')) classes.push(classMap.sm);
    if (classMap.md && isBetween('md', 'lg')) classes.push(classMap.md);
    if (classMap.lg && isBetween('lg', 'xl')) classes.push(classMap.lg);
    if (classMap.xl && isBetween('xl', 'xxl')) classes.push(classMap.xl);
    if (classMap.xxl && isGreaterThan('xxl')) classes.push(classMap.xxl);

    if (classMap.mobile && isMobile()) classes.push(classMap.mobile);
    if (classMap.tablet && isTablet()) classes.push(classMap.tablet);
    if (classMap.desktop && isDesktop()) classes.push(classMap.desktop);

    return classes.join(' ');
  }, [isLessThan, isBetween, isGreaterThan, isMobile, isTablet, isDesktop]);

  const getMediaQuery = useCallback((breakpoint: Breakpoint, type: 'min' | 'max' = 'min'): string => {
    const value = BREAKPOINTS[breakpoint];
    return type === 'min'
      ? `@media (min-width: ${value}px)`
      : `@media (max-width: ${value - 0.02}px)`;
  }, []);

  const getMediaQueryRange = useCallback((minBreakpoint: Breakpoint, maxBreakpoint: Breakpoint): string => {
    const minValue = BREAKPOINTS[minBreakpoint];
    const maxValue = BREAKPOINTS[maxBreakpoint] - 0.02;
    return `@media (min-width: ${minValue}px) and (max-width: ${maxValue}px)`;
  }, []);

  const getOrientationMediaQuery = useCallback((orientationType: Orientation): string => {
    return `@media (orientation: ${orientationType})`;
  }, []);

  return {
    windowWidth,
    windowHeight,
    breakpoints: BREAKPOINTS,
    responsive: RESPONSIVE,
    breakpointInfo,
    currentBreakpoint,
    orientation,
    isGreaterThan,
    isLessThan,
    isBetween,
    isMobile,
    isTablet,
    isDesktop,
    getResponsiveClasses,
    getMediaQuery,
    getMediaQueryRange,
    getOrientationMediaQuery,
    dimensions: DIMENSIONS
  };
};

export default useResponsive;
